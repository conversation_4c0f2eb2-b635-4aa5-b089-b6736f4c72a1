<template>
  <BaseLayout 
    title="文档详情" 
    description="查看文档分析结果和详细信息"
    :breadcrumbs="[
      { text: '首页', to: '/' },
      { text: '我的文档', to: '/documents' },
      { text: documentData?.filename || '文档详情', to: '#' }
    ]"
  >
    <template #header-actions>
      <div class="flex flex-wrap gap-3">
        <BaseButton 
          @click="downloadOriginal" 
          variant="secondary" 
          prepend-icon="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          :disabled="!documentData || documentData.status !== 'completed'"
        >
          下载原文档
        </BaseButton>
        <BaseButton 
          @click="downloadReport" 
          variant="primary" 
          prepend-icon="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          :disabled="!documentData || documentData.status !== 'completed'"
        >
          下载分析报告
        </BaseButton>
      </div>
    </template>

    <div v-if="loading" class="text-center py-16">
      <svg class="animate-spin h-8 w-8 text-gray-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="mt-4 text-gray-600 dark:text-gray-300">正在加载文档详情...</p>
    </div>

    <div v-else-if="error" class="text-center py-16">
       <p class="text-red-500">{{ error }}</p>
       <BaseButton @click="fetchDocumentDetail" class="mt-4">重试</BaseButton>
    </div>

    <div v-else-if="documentData" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- 主要内容区域 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 文档信息头部 -->
        <BaseCard>
          <div class="flex items-center space-x-4">
            <div class="file-icon docx">DOC</div>
            <div>
              <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ documentData.filename }}</h1>
              <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
                <span>任务ID: {{ documentData.task_id }}</span>
                <span>•</span>
                <span>文件大小: {{ formatFileSize(documentData.file_size) }}</span>
                <span>•</span>
                <span>上传时间: {{ formatDateTime(documentData.upload_time) }}</span>
                <span>•</span>
                <span :class="getStatusClass(documentData.status)">{{ getStatusText(documentData.status) }}</span>
              </div>
            </div>
          </div>
        </BaseCard>

        <!-- 分析概览 - 使用新组件 -->
        <DocumentAnalysisPanel 
          v-if="documentData.document_id"
          :document-id="documentData.document_id"
          :auto-load="true"
        />

        <!-- 文档信息 -->
        <BaseCard v-if="documentData">
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">文档信息</h2>
          </template>
          
          <div class="space-y-6">
            <!-- 论文题目 - 单独一行 -->
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">论文题目</span>
              <span class="text-base text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700/50 px-4 py-3 rounded-lg">
                {{ getDocumentTitle() }}
              </span>
            </div>
            
            <!-- 其他信息 - 三列布局 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">作者</span>
                <span class="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700/50 px-3 py-2 rounded-lg">
                  {{ getDocumentAuthor() }}
                </span>
              </div>
              
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">学号</span>
                <span class="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700/50 px-3 py-2 rounded-lg">
                  {{ getStudentId() }}
                </span>
              </div>
              
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">院系</span>
                <span class="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700/50 px-3 py-2 rounded-lg">
                  {{ getDepartment() }}
                </span>
              </div>
              
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">专业</span>
                <span class="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700/50 px-3 py-2 rounded-lg">
                  {{ getDocumentMajor() }}
                </span>
              </div>
              
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">指导老师</span>
                <span class="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700/50 px-3 py-2 rounded-lg">
                  {{ getAdvisor() }}
                </span>
              </div>
              
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">论文时间</span>
                <span class="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700/50 px-3 py-2 rounded-lg">
                  {{ getPaperDate() }}
                </span>
              </div>
            </div>
            
            <!-- 质量等级 - 单独一行 -->
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">质量等级</span>
              <div class="flex">
                <span class="text-sm font-medium px-4 py-2 rounded-lg inline-block" :class="getQualityGradeClass()">
                  {{ getQualityGrade() }}
                </span>
              </div>
            </div>
          </div>
        </BaseCard>

        <!-- 文档统计 -->
        <BaseCard v-if="documentData">
          <template #header>
            <div class="flex items-center justify-between">
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">文档统计</h2>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ getDataSourceText() }}
              </div>
            </div>
          </template>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                {{ documentData.statistics.pages || 0 }}
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">页数</p>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                {{ formatNumber(documentData.statistics.words || 0) }}
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">字数</p>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
                {{ documentData.statistics.tables || 0 }}
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">表格数</p>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-1">
                {{ documentData.statistics.images || 0 }}
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">图片数</p>
            </div>
            
            <!-- 更多统计信息 -->
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-1">
                {{ documentData.statistics.formulas || 0 }}
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">公式数</p>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-2xl font-bold text-teal-600 dark:text-teal-400 mb-1">
                {{ formatNumber(documentData.statistics.paragraphs || 0) }}
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">段落数</p>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-2xl font-bold text-pink-600 dark:text-pink-400 mb-1">
                {{ documentData.statistics.footnotes || 0 }}
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">脚注数</p>
            </div>
            
            <div class="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div class="text-2xl font-bold text-amber-600 dark:text-amber-400 mb-1">
                {{ documentData.statistics.references || 0 }}
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-300">参考文献</p>
            </div>
          </div>
          
          <!-- 数据加载状态提示 -->
          <div v-if="loading" class="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
            正在加载完整的统计信息...
          </div>
          
          <!-- 无数据提示 -->
          <div v-else-if="!hasAnyStatistics()" class="mt-4 text-center">
            <div class="text-gray-400 dark:text-gray-600 mb-2">
              <svg class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              暂无统计信息，文档可能还在处理中
            </p>
            <BaseButton 
              @click="fetchDocumentDetail" 
              variant="secondary" 
              size="sm" 
              class="mt-2"
            >
              刷新数据
            </BaseButton>
          </div>
        </BaseCard>

        <!-- 问题详情 - 使用新组件 -->
        <DocumentIssuesPanel 
          v-if="documentData.document_id"
          :document-id="documentData.document_id"
          :auto-load="true"
        />

      </div>

      <!-- 侧边栏 -->
      <div class="space-y-6">
        <!-- 检测信息 -->
        <BaseCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">检测信息</h3>
          </template>
          
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">检测类型</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ getTaskTypeText(documentData.task_detail?.task_type) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">检测标准</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ getDetectionStandard() }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">开始时间</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ formatDateTime(documentData.task_detail?.created_at) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">完成时间</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ formatDateTime(documentData.task_detail?.completed_at) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">处理用时</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ calculateDuration(documentData.task_detail?.created_at, documentData.task_detail?.completed_at) }}</span>
            </div>
          </div>
        </BaseCard>

        <!-- 图片信息 -->
        <BaseCard v-if="documentImages && documentImages.length > 0">
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">文档图片</h3>
          </template>
          
          <div class="space-y-3">
            <div v-for="image in documentImages.slice(0, 3)" 
                 :key="image.image_id"
                 class="flex items-center space-x-3 p-2 border border-gray-200 dark:border-gray-600 rounded">
              <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
                <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
              </div>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ image.caption || `图片 ${image.position}` }}
                </p>
                <p class="text-xs text-gray-600 dark:text-gray-400">
                  {{ Math.round(image.display_width) }}×{{ Math.round(image.display_height) }}
                  <span v-if="image.page_number"> · 第{{ image.page_number }}页</span>
                </p>
              </div>
            </div>
            
            <div v-if="documentImages.length > 3" class="text-center pt-2">
              <BaseButton variant="secondary" size="sm" @click="showAllImages">
                查看全部 {{ documentImages.length }} 张图片
              </BaseButton>
            </div>
          </div>
        </BaseCard>

        <!-- 操作面板 -->
        <BaseCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">快速操作</h3>
          </template>
          
          <div class="space-y-3">
            <BaseButton 
              @click="viewStatisticsReport" 
              variant="primary" 
              class="w-full" 
              prepend-icon="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              :disabled="documentData.status !== 'completed'"
            >
              统计报告
            </BaseButton>
            <BaseButton 
              @click="reanalyze" 
              variant="secondary" 
              class="w-full" 
              prepend-icon="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              :disabled="documentData.status === 'processing'"
            >
              重新分析
            </BaseButton>
            <BaseButton 
              @click="shareReport" 
              variant="secondary" 
              class="w-full" 
              prepend-icon="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
              :disabled="documentData.status !== 'completed'"
            >
              分享报告
            </BaseButton>
            <BaseButton 
              @click="deleteDocument" 
              variant="danger" 
              class="w-full" 
              prepend-icon="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            >
              删除文档
            </BaseButton>
          </div>
        </BaseCard>


      </div>
    </div>
  </BaseLayout>

  <!-- 重新分析选项对话框 -->
  <ReanalyzeOptionsDialog
    :is-visible="showReanalyzeDialog"
    @confirm="handleReanalyzeConfirm"
    @cancel="handleReanalyzeCancel"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseButton from '@/components/BaseButton.vue'
import DocumentAnalysisPanel from '@/components/DocumentAnalysisPanel.vue'
import DocumentIssuesPanel from '@/components/DocumentIssuesPanel.vue'
import TaskProgressCard from '@/components/TaskProgressCard.vue'
import ReanalyzeOptionsDialog from '@/components/ReanalyzeOptionsDialog.vue'
import { $confirm } from '@/utils/useConfirm'
import { DocumentApi } from '@/services/documentApi'
import { TaskApi } from '@/services/taskApi'
import type { 
  DocumentDetail, 
  TaskDetail, 
  DocumentContent,
  DocumentImage,
  PaperCheckResult
} from '@/types'
import { useNotifications } from '@/utils/useNotifications'

// 服务
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const documentApiService = new DocumentApi()
const taskApi = new TaskApi()
const { addNotification } = useNotifications()

// 状态
const documentData = ref<DocumentDetail | null>(null)
const documentContent = ref<DocumentContent | null>(null)
const documentImages = ref<DocumentImage[]>([])
const paperCheckResult = ref<PaperCheckResult | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// 重新分析对话框
const showReanalyzeDialog = ref(false)

// 类型定义
interface ReanalyzeOptions {
  analysisType: string
  standard: string
}

// 方法
const fetchDocumentDetail = async () => {
  loading.value = true
  error.value = null
  
  try {
    const taskId = route.params.id as string
    if (!taskId) {
      throw new Error("任务ID无效")
    }
    
    console.log('正在加载任务详情:', taskId)
    
    // 1. 获取任务详情（主要数据源）
    const taskDetail = await taskApi.getTask(taskId)
    console.log('任务详情:', taskDetail)
    
    // 2. 尝试根据任务获取文档信息
    let documentInfo = null
    let documentId = null
    
    try {
      documentInfo = await documentApiService.getDocumentByTask(taskId)
      documentId = documentInfo.document_id
      console.log('文档信息:', documentInfo)
    } catch (err) {
      console.warn('无法通过任务获取文档信息:', err)
      // 使用任务ID作为文档ID的备用方案
      documentId = taskId
    }
    
    // 3. 从任务结果预提取统计信息
    const taskResult = taskDetail.result || {}
    console.log('任务结果:', taskResult)
    
    const extractedStats = {
      pages: getValueFromResult(taskResult, ['pages', 'page_count', 'total_pages'], 0),
      words: getValueFromResult(taskResult, ['words', 'word_count', 'total_words'], 0),
      paragraphs: getValueFromResult(taskResult, ['paragraphs', 'paragraph_count'], 0),
      tables: getValueFromResult(taskResult, ['tables', 'table_count'], 0),
      images: getValueFromResult(taskResult, ['images', 'image_count'], 0),
      formulas: getValueFromResult(taskResult, ['formulas', 'formula_count'], 0),
      references: getValueFromResult(taskResult, ['references', 'reference_count'], 0),
      footnotes: getValueFromResult(taskResult, ['footnotes', 'footnote_count'], 0),
      charts: 0,
      spaces: 0,
      endnotes: 0
    }
    
    console.log('提取的统计信息:', extractedStats)
    
    // 4. 构建基础文档详情
    const fullDetail: DocumentDetail = {
      id: documentId || taskId,
      document_id: documentId,
      filename: taskDetail.filename,
      task_id: taskDetail.task_id,
      file_size: taskDetail.file_size || 0,
      upload_time: taskDetail.created_at,
      status: taskDetail.status,
      
      // 从任务结果提取分析概览
      analysis: {
        score: extractScore(taskDetail.result),
        errors: extractErrorCount(taskDetail.result),
        warnings: extractWarningCount(taskDetail.result),
        suggestions: extractSuggestionCount(taskDetail.result),
        compliance_status: extractComplianceStatus(taskDetail.result)
      },
      
      // 初始化为空，后续异步加载
      issues: [],
      statistics: extractedStats,
      
      detection: {
        type: getTaskTypeText(taskDetail.task_type),
        standard: 'GB/T 7714-2015',
        start_time: formatDateTime(taskDetail.created_at),
        end_time: formatDateTime(taskDetail.completed_at),
        duration: calculateDuration(taskDetail.created_at, taskDetail.completed_at),
      },
      

      
      // 保存原始任务信息 (处理类型兼容性)
      task_detail: {
        ...taskDetail,
        steps: taskDetail.steps ? (
          // 🔥 修复：处理新的steps对象格式
          Array.isArray(taskDetail.steps) 
            ? taskDetail.steps.map(step => ({
                step_name: (step as any).name || (step as any).step_name || step.status || '',
                status: step.status as 'completed' | 'failed' | 'pending' | 'running',
                started_at: (step as any).started_at,
                completed_at: (step as any).completed_at,
                message: (step as any).message || ''
              }))
            : Object.values(taskDetail.steps).map((step: any) => ({
                step_name: step.name || step.step_name || step.status || '',
                status: step.status as 'completed' | 'failed' | 'pending' | 'running',
                started_at: step.start_time || step.started_at,
                completed_at: step.end_time || step.completed_at,
                message: step.description || step.message || ''
              }))
        ) : []
      } as any
    }
    
    documentData.value = fullDetail
    
    // 5. 异步加载额外数据
    if (documentId) {
      await loadAdditionalData(documentId)
    }
    
  } catch (e: any) {
    console.error('加载文档详情失败:', e)
    const errorMessage = e.message || '加载文档详情失败，请稍后重试。'
    error.value = errorMessage
    addNotification({
      type: 'error',
      title: '加载失败',
      message: errorMessage,
    })
  } finally {
    loading.value = false
  }
}

// 加载额外的文档数据
const loadAdditionalData = async (documentId: string) => {
  try {
    console.log('开始加载额外数据:', documentId)
    
    // 并行加载文档内容、图片、分析结果等信息
    const [content, images, analysis] = await Promise.allSettled([
      documentApiService.getDocumentContent(documentId).catch(err => {
        console.warn('获取文档内容失败:', err)
        return null
      }),
      documentApiService.getDocumentImages(documentId).catch(err => {
        console.warn('获取文档图片失败:', err)
        return []
      }),
      documentApiService.getDocumentAnalysis(documentId).catch(err => {
        console.warn('获取文档分析失败:', err)
        return null
      })
    ])
    
    // 处理文档内容
    if (content.status === 'fulfilled' && content.value) {
      documentContent.value = content.value
      console.log('文档内容:', content.value)
      
      // 合并统计信息 - 优先使用API返回的数据
      if (documentData.value) {
        const currentStats = documentData.value.statistics
        documentData.value.statistics = {
          pages: content.value.pages || currentStats.pages,
          words: content.value.words || currentStats.words,
          paragraphs: currentStats.paragraphs,
          charts: currentStats.charts,
          references: currentStats.references,
          tables: content.value.tables || currentStats.tables,
          images: content.value.images || currentStats.images,
          formulas: currentStats.formulas,
          spaces: currentStats.spaces,
          footnotes: currentStats.footnotes,
          endnotes: currentStats.endnotes
        }
        console.log('更新后的统计信息:', documentData.value.statistics)
      }
    }
    
    // 处理图片数据
    if (images.status === 'fulfilled') {
      documentImages.value = images.value || []
      console.log('文档图片:', documentImages.value)
    }
    
    // 处理分析结果
    if (analysis.status === 'fulfilled' && analysis.value) {
      console.log('文档分析结果:', analysis.value)
      if (documentData.value) {
        // 更新分析概览信息
        documentData.value.analysis = {
          score: analysis.value.overall_score || documentData.value.analysis.score,
          errors: analysis.value.major_problems || documentData.value.analysis.errors,
          warnings: analysis.value.minor_problems || documentData.value.analysis.warnings,
          suggestions: 0,
          compliance_status: analysis.value.compliance_status || documentData.value.analysis.compliance_status
        }
      }
    }
    
  } catch (err) {
    console.warn('加载额外数据失败:', err)
  }
}

// 从任务结果提取数据的辅助函数
const getValueFromResult = (result: any, keys: string[], defaultValue: any = 0): any => {
  if (!result) return defaultValue
  
  // 尝试直接访问键
  for (const key of keys) {
    if (result[key] !== undefined && result[key] !== null) {
      return result[key]
    }
  }
  
  // 尝试从嵌套对象中访问
  if (result.analysis_result) {
    for (const key of keys) {
      if (result.analysis_result[key] !== undefined && result.analysis_result[key] !== null) {
        return result.analysis_result[key]
      }
    }
    
    // 🔥 关键修复：访问analysis_result.content_stats路径
    if (result.analysis_result.content_stats) {
      for (const key of keys) {
        if (result.analysis_result.content_stats[key] !== undefined && result.analysis_result.content_stats[key] !== null) {
          return result.analysis_result.content_stats[key]
        }
      }
    }
  }
  
  // 尝试从content_stats中访问
  if (result.content_stats) {
    for (const key of keys) {
      if (result.content_stats[key] !== undefined && result.content_stats[key] !== null) {
        return result.content_stats[key]
      }
    }
  }
  
  // 尝试从document_info中访问
  if (result.document_info) {
    for (const key of keys) {
      if (result.document_info[key] !== undefined && result.document_info[key] !== null) {
        return result.document_info[key]
      }
    }
  }
  
  return defaultValue
}

const extractScore = (result: any): number => {
  if (!result) return 0
  return result.overall_score || 
         result.compliance_score || 
         result.format_score || 
         result.analysis_result?.overall_score || 
         0
}

const extractErrorCount = (result: any): number => {
  if (!result) return 0
  return result.problem_breakdown?.severe || 
         result.major_problems || 
         result.problems_found || 
         result.analysis_result?.problem_breakdown?.severe ||
         0
}

const extractWarningCount = (result: any): number => {
  if (!result) return 0
  return result.problem_breakdown?.moderate || 
         result.moderate_problems ||
         result.analysis_result?.problem_breakdown?.moderate ||
         0
}

const extractSuggestionCount = (result: any): number => {
  if (!result) return 0
  return result.problem_breakdown?.minor || 
         result.minor_problems ||
         result.analysis_result?.problem_breakdown?.minor ||
         0
}

const extractComplianceStatus = (result: any): string => {
  if (!result) return 'unknown'
  return result.compliance_status || 
         result.analysis_result?.compliance_status ||
         'unknown'
}

const getResultValue = (key: string, defaultValue: any = 0): any => {
  if (!documentData.value?.task_detail?.result) return defaultValue
  
  const result = documentData.value.task_detail.result
  return result[key] || 
         result.analysis_result?.[key] || 
         result.content_stats?.[key] ||
         defaultValue
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN')
}

const formatDateTime = (dateString?: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit', 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const calculateDuration = (start?: string, end?: string): string => {
  if (!start || !end) return 'N/A'
  try {
    const startDate = new Date(start)
    const endDate = new Date(end)
    const diffMs = endDate.getTime() - startDate.getTime()
    if (diffMs < 0) return 'N/A'
    
    const diffSec = Math.floor(diffMs / 1000)
    const minutes = Math.floor(diffSec / 60)
    const seconds = diffSec % 60

    if (minutes > 0) {
      return `${minutes}分${seconds}秒`
    }
    return `${seconds}秒`
  } catch (e) {
    return 'N/A'
  }
}

const getStatusClass = (status: string) => {
  const classes = {
    'completed': 'status-badge status-completed',
    'processing': 'status-badge status-processing',
    'failed': 'status-badge status-failed',
    'pending': 'status-badge status-pending',
    'cancelled': 'status-badge status-failed',
  }
  return classes[status as keyof typeof classes] || 'status-badge'
}

const getStatusText = (status: string) => {
  const texts = {
    'completed': '已完成',
    'processing': '处理中',
    'failed': '失败',
    'pending': '等待中',
    'cancelled': '已取消',
  }
  return texts[status as keyof typeof texts] || status
}

const getTaskTypeText = (taskType?: string) => {
  const texts = {
    'paper_check': '论文检测',
    'format_check': '格式检查',
    'content_analysis': '内容分析',
    'document_parse': '文档解析'
  }
  return texts[taskType as keyof typeof texts] || taskType || '未知类型'
}

const downloadOriginal = async () => {
  if (!documentData.value) return
  
  try {
    // 根据实际API调用下载原文档
    await documentApiService.exportDocumentReport(
      documentData.value.task_id, 
      'pdf', 
      true, 
      true
    )
    addNotification({ 
      type: 'success', 
      title: '下载开始', 
      message: '原始文档下载已开始' 
    })
  } catch (e: any) {
    addNotification({ 
      type: 'error', 
      title: '下载失败', 
      message: e.message || '无法下载原始文档' 
    })
  }
}

const downloadReport = async () => {
  if (!documentData.value) return
  
  try {
    await documentApiService.exportDocumentReport(
      documentData.value.document_id || documentData.value.task_id, 
      'pdf', 
      true, 
      true
    )
    addNotification({ 
      type: 'success', 
      title: '下载开始', 
      message: '分析报告下载已开始' 
    })
  } catch (e: any) {
    addNotification({ 
      type: 'error', 
      title: '下载失败', 
      message: e.message || '无法下载分析报告' 
    })
  }
}

const reanalyze = async () => {
  if (!documentData.value) return
  
  // 显示选项选择对话框
  showReanalyzeDialog.value = true
}

// 处理重新分析选项确认
const handleReanalyzeConfirm = async (options: ReanalyzeOptions) => {
  showReanalyzeDialog.value = false
  
  if (!documentData.value) return
  
  try {
    // 构建分析选项
    const analysisOptions: Record<string, any> = {}
    
    // 根据选择的检测标准自动设置检测选项
    if (options.analysisType === 'paper_check') {
      analysisOptions.standard = options.standard
      
      // 根据检测标准自动启用相应的检测选项
      if (options.standard === 'gbt_7713_1_2006') {
        // GB/T 7713.1-2006：学位论文编写规则 - 结构检查 + 格式检查
        analysisOptions.check_format = true
        analysisOptions.check_structure = true
        analysisOptions.check_citation = false
        analysisOptions.check_reference = false
      } else if (options.standard === 'gbt_7714_2015') {
        // GB/T 7714-2015：参考文献著录规则 - 引用格式检查
        analysisOptions.check_format = false
        analysisOptions.check_structure = false
        analysisOptions.check_citation = true
        analysisOptions.check_reference = true
      } else if (options.standard === 'hbkj_bachelor_2024') {
        // 河北科技学院：本科论文检查 - 全面检查
        analysisOptions.check_format = true
        analysisOptions.check_structure = true
        analysisOptions.check_citation = true
        analysisOptions.check_reference = true
      } else {
        // 默认情况：启用所有检测
        analysisOptions.check_format = true
        analysisOptions.check_structure = true
        analysisOptions.check_citation = true
        analysisOptions.check_reference = true
      }
    }
    
    // 调用重新分析接口
    const response = await documentApiService.reanalyzeDocument(
      String(documentData.value.document_id || documentData.value.task_id),
      options.analysisType, 
      analysisOptions
    )
    
    addNotification({ 
      type: 'success', 
      title: '重新分析', 
      message: `重新分析已开始，剩余次数：${response.remaining_balance || '未知'}` 
    })
    
    // 重新分析启动后，刷新页面数据以显示新的任务信息
    if (response.task_id) {
      // 等待一小段时间让后端处理，然后刷新页面数据
      setTimeout(() => {
        fetchDocumentDetail()
      }, 1000)
    }
    
  } catch (error: any) {
    console.error('重新分析失败:', error)
    
    // 根据错误类型显示不同提示
    if (error.response?.status === 402) {
      addNotification({ 
        type: 'error', 
        title: '余额不足', 
        message: '检测次数不足，请充值后再试' 
      })
    } else if (error.response?.status === 404) {
      addNotification({ 
        type: 'error', 
        title: '文档不存在', 
        message: '未找到原始文档，无法重新分析' 
      })
    } else {
      addNotification({ 
        type: 'error', 
        title: '重新分析失败', 
        message: error.response?.data?.message || '系统错误，请稍后重试' 
      })
    }
  }
}

// 处理重新分析选项取消
const handleReanalyzeCancel = () => {
  showReanalyzeDialog.value = false
}

// 🔥 修复: 统一使用后端返回的标准名称，确保与config.py一致
const getDetectionStandard = (): string => {
  // 优先使用任务结果中的标准名称（这是从config.py转换的正确名称）
  if (documentData.value?.task_detail?.result?.standard_name) {
    return documentData.value.task_detail.result.standard_name;
  }

  // 回退到根级别的 standard_name
  if (documentData.value?.task_detail?.standard_name) {
    return documentData.value.task_detail.standard_name;
  }

  // 最后的回退值
  return '标准检测';
}

// 获取分析类型名称
const getAnalysisTypeName = (type: string): string => {
  const typeNames: Record<string, string> = {
    'paper_check': '论文检测',
    'content_analysis': '内容分析', 
    'format_check': '格式检查'
  }
  return typeNames[type] || type
}

const shareReport = () => {
  if (!documentData.value) return
  
  const shareUrl = `${window.location.origin}/document-detail/${documentData.value.task_id}`
  navigator.clipboard.writeText(shareUrl).then(() => {
    addNotification({ 
      type: 'success', 
      title: '链接已复制', 
      message: '分享链接已复制到剪贴板' 
    })
  }, () => {
    addNotification({ 
      type: 'error', 
      title: '复制失败', 
      message: '无法自动复制链接' 
    })
  })
}

const viewStatisticsReport = () => {
  if (!documentData.value) return
  
  // 跳转到统计报告页面
  router.push({
    name: 'StatisticsReport',
    params: {
      id: documentData.value.task_id || documentData.value.document_id
    }
  })
}

const deleteDocument = async () => {
  if (!documentData.value) return

  const result = await $confirm.danger(
    `确定要删除文档 "${documentData.value.filename}" 及其所有分析记录吗？此操作无法撤销。`, 
    {
      title: '删除文档',
      confirmText: '确定删除',
      cancelText: '取消'
    }
  )

  if (result) {
    try {
      const doc = documentData.value
      addNotification({ 
        type: 'info', 
        title: '正在删除', 
        message: `正在删除文档 "${doc.filename}"...`
      })

      // 在文档详情页，主要标识符是 task_id。删除任务应该会级联删除后端的相关数据。
      await taskApi.deleteTask(doc.task_id)

      addNotification({ 
        type: 'success', 
        title: '删除成功', 
        message: `文档 "${doc.filename}" 已成功删除。`
      })
      
      // 删除成功后重定向到文档列表
      router.push('/documents')

    } catch (e: any) {
      addNotification({ 
        type: 'error', 
        title: '删除失败', 
        message: e.message || '无法删除该文档' 
      })
    }
  }
}

const showAllImages = () => {
  addNotification({ 
    type: 'info', 
    title: '功能待实现', 
    message: '查看全部图片功能正在开发中' 
  })
}

const getDataSourceText = (): string => {
  if (!documentData.value) return ''
  
  const hasTaskData = documentData.value.task_detail?.result
  const hasContentData = documentContent.value
  
  if (hasContentData && hasTaskData) {
    return '数据来源: 任务分析 + 文档API'
  } else if (hasTaskData) {
    return '数据来源: 任务分析结果'
  } else if (hasContentData) {
    return '数据来源: 文档API'
  } else {
    return '数据来源: 基础信息'
  }
}

const hasAnyStatistics = (): boolean => {
  if (!documentData.value?.statistics) return false
  
  const stats = documentData.value.statistics
  return stats.pages > 0 || 
         stats.words > 0 || 
         stats.paragraphs > 0 || 
         stats.tables > 0 || 
         stats.images > 0 || 
         stats.formulas > 0 || 
         stats.references > 0 || 
         stats.footnotes > 0
}

// 获取文档标题
const getDocumentTitle = (): string => {
  if (!documentData.value) return '暂无标题'
  
  // 🔥 优化：从多个数据源获取标题信息

  // 1. 从任务结果的封面页信息中获取
  const taskResult = documentData.value.task_detail?.result
  if (taskResult?.document_info?.cover_page_info?.title) {
    return taskResult.document_info.cover_page_info.title
  }

  // 2. 从任务结果的根级document_info中获取（修正路径）
  if (taskResult?.document_info?.cover_page_info?.title) {
    return taskResult.document_info.cover_page_info.title
  }

  // 3. 从文档内容中获取
  if (documentContent.value?.title) {
    return documentContent.value.title
  }

  // 4. 从任务结果中获取
  if (taskResult?.title) {
    return taskResult.title
  }

  // 5. 从任务选项中获取（如果用户在上传时指定了标题）
  const taskOptions = documentData.value.task_detail?.analysis_options
  if (taskOptions?.title) {
    return taskOptions.title
  }
  
  // 从文件名推断（去掉扩展名）
  const filename = documentData.value.filename
  if (filename) {
    const title = filename.replace(/\.(doc|docx)$/i, '')
    if (title !== filename) {
      return title
    }
  }
  
  return '暂无标题'
}

// 获取文档作者
const getDocumentAuthor = (): string => {
  if (!documentData.value) return '暂无作者信息'

  // 🔥 优化：从多个数据源获取作者信息

  // 1. 从任务结果的封面页信息中获取
  const taskResult = documentData.value.task_detail?.result
  if (taskResult?.document_info?.cover_page_info?.author) {
    return taskResult.document_info.cover_page_info.author
  }

  // 2. 从任务结果的根级document_info中获取（修正路径）
  if (taskResult?.document_info?.cover_page_info?.author) {
    return taskResult.document_info.cover_page_info.author
  }

  // 3. 从文档内容中获取
  if (documentContent.value?.author) {
    return documentContent.value.author
  }

  // 4. 从任务结果中获取
  if (taskResult?.author) {
    return taskResult.author
  }

  // 5. 从当前用户信息推断
  if (userStore.currentUser?.full_name) {
    return userStore.currentUser.full_name
  }

  return '暂无作者信息'
}

// 获取文档专业
const getDocumentMajor = (): string => {
  if (!documentData.value) return '暂无专业信息'

  // 🔥 优化：从多个数据源获取专业信息

  // 1. 从任务结果的封面页信息中获取
  const taskResult = documentData.value.task_detail?.result
  if (taskResult?.document_info?.cover_page_info?.major) {
    return taskResult.document_info.cover_page_info.major
  }

  // 2. 从任务结果的根级document_info中获取（修正路径）
  if (taskResult?.document_info?.cover_page_info?.major) {
    return taskResult.document_info.cover_page_info.major
  }

  // 3. 从任务结果中获取
  if (taskResult?.major) {
    return taskResult.major
  }

  // 4. 从任务选项中获取
  const taskOptions = documentData.value.task_detail?.analysis_options
  if (taskOptions?.major) {
    return taskOptions.major
  }

  return '暂无专业信息'
}

// 获取质量等级
const getQualityGrade = (): string => {
  if (!documentData.value) return '未评级'
  
  // 基于分析结果计算质量等级
  const score = documentData.value.analysis?.score || 0
  const errors = documentData.value.analysis?.errors || 0
  const warnings = documentData.value.analysis?.warnings || 0
  
  if (score >= 90 && errors === 0) {
    return '优秀'
  } else if (score >= 80 && errors <= 1) {
    return '良好'
  } else if (score >= 70 && errors <= 3) {
    return '中等'
  } else if (score >= 60) {
    return '合格'
  } else {
    return '需要改进'
  }
}

// 获取质量等级的样式类
const getQualityGradeClass = (): string => {
  const grade = getQualityGrade()
  
  const classMap: { [key: string]: string } = {
    '优秀': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    '良好': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    '中等': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
    '合格': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
    '需要改进': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    '未评级': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
  }
  
  return classMap[grade] || classMap['未评级']
}

// 获取学号
const getStudentId = (): string => {
  if (!documentData.value) return '暂无学号信息'

  // 🔥 优化：从多个数据源获取学号信息

  // 1. 从任务结果的封面页信息中获取
  const taskResult = documentData.value.task_detail?.result
  if (taskResult?.document_info?.cover_page_info?.student_id) {
    return taskResult.document_info.cover_page_info.student_id
  }

  // 2. 从任务结果的根级document_info中获取（修正路径）
  if (taskResult?.document_info?.cover_page_info?.student_id) {
    return taskResult.document_info.cover_page_info.student_id
  }

  // 3. 从当前用户信息推断（如果用户名是学号）
  if (userStore.currentUser?.username && /^\d+$/.test(userStore.currentUser.username)) {
    return userStore.currentUser.username
  }

  return '暂无学号信息'
}

// 获取院系
const getDepartment = (): string => {
  if (!documentData.value) return '暂无院系信息'

  // 🔥 优化：从多个数据源获取院系信息

  // 1. 从任务结果的封面页信息中获取
  const taskResult = documentData.value.task_detail?.result
  if (taskResult?.document_info?.cover_page_info?.department) {
    return taskResult.document_info.cover_page_info.department
  }

  // 2. 从任务结果的根级document_info中获取（修正路径）
  if (taskResult?.document_info?.cover_page_info?.department) {
    return taskResult.document_info.cover_page_info.department
  }

  return '暂无院系信息'
}

// 获取指导老师
const getAdvisor = (): string => {
  if (!documentData.value) return '暂无指导老师信息'

  // 🔥 优化：从多个数据源获取指导老师信息

  // 1. 从任务结果的封面页信息中获取
  const taskResult = documentData.value.task_detail?.result
  if (taskResult?.document_info?.cover_page_info?.advisor) {
    return taskResult.document_info.cover_page_info.advisor
  }

  // 2. 从任务结果的根级document_info中获取（修正路径）
  if (taskResult?.document_info?.cover_page_info?.advisor) {
    return taskResult.document_info.cover_page_info.advisor
  }

  return '暂无指导老师信息'
}

// 获取论文时间
const getPaperDate = (): string => {
  if (!documentData.value) return '暂无时间信息'

  // 🔥 优化：从多个数据源获取论文时间信息

  // 1. 从任务结果的封面页信息中获取
  const taskResult = documentData.value.task_detail?.result
  if (taskResult?.document_info?.cover_page_info?.date) {
    return taskResult.document_info.cover_page_info.date
  }

  // 2. 从任务结果的根级document_info中获取（修正路径）
  if (taskResult?.document_info?.cover_page_info?.date) {
    return taskResult.document_info.cover_page_info.date
  }

  // 3. 从任务创建时间推断
  if (documentData.value.task_detail?.created_at) {
    const createdDate = new Date(documentData.value.task_detail.created_at)
    return createdDate.toLocaleDateString('zh-CN')
  }

  return '暂无时间信息'
}

// 生命周期
onMounted(() => {
  if (!userStore.isAuthenticated) {
    router.push('/auth')
    return
  }
  fetchDocumentDetail()
})
</script>

<style scoped>
.file-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center text-xs font-bold text-white flex-shrink-0 uppercase;
}

.file-icon.docx {
  @apply bg-blue-600;
}

.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-completed {
  @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300;
}

.status-processing {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300;
}

.status-failed {
  @apply bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300;
}
</style> 