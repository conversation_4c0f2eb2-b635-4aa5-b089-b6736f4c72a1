<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
    <div class="max-w-7xl mx-auto px-4 py-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-16">
        <svg class="animate-spin h-8 w-8 text-gray-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="mt-4 text-gray-600 dark:text-gray-300">正在加载统计报告...</p>
      </div>
      <!-- 错误提示 -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-xl p-6 mb-6">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-red-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-1.414 1.414A9 9 0 105.636 18.364l1.414-1.414A7 7 0 1116.95 7.05z"/>
          </svg>
          <div>
            <h3 class="text-red-800 font-medium">加载统计报告失败</h3>
            <p class="text-red-700 text-sm mt-1">{{ error }}</p>
          </div>
        </div>
      </div>
      <!-- 主体内容 -->
      <div v-else>
        <!-- 页面标题栏 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">统计报告</h1>
                <p class="text-gray-600 dark:text-gray-300 text-sm">论文格式检测 · 大学生版</p>
              </div>
            </div>
            <div class="text-right">
              <div class="flex items-center space-x-2">
                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs font-medium rounded-md">NO.</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ mixedData.document_id }}</span>
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">{{ getCheckedTime() }}</div>
            </div>
          </div>

        </div>
        
          <!-- 文档基本信息 -->
          <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600 p-5 mb-6 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-32 h-32 bg-blue-100 dark:bg-blue-800 opacity-20 rounded-full -mr-16 -mt-16"></div>
            
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-9 h-9 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">文档信息</h2>
                    <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Document Information</p>
                  </div>
                </div>
                
                <div class="flex items-center space-x-2 bg-white dark:bg-gray-800 rounded-full px-3 py-1 shadow-sm border border-gray-200 dark:border-gray-600">
                  <div class="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span class="text-xs font-medium text-gray-600 dark:text-gray-300">检测完成</span>
                </div>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-5 gap-4">
                <div class="lg:col-span-4 space-y-3">
                  <!-- 论文标题 -->
                  <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                    <div class="flex items-start space-x-3">
                      <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center flex-shrink-0 mt-0.5">
                        <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-1">
                          <span class="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase">论文题目</span>
                          <span class="px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs rounded font-medium">{{ mixedData.document_info.degree_type }}</span>
                        </div>
                        <p class="text-sm font-semibold text-gray-900 dark:text-white leading-snug">{{ mixedData.document_info.title }}</p>
                      </div>
                    </div>
                  </div>

                  <!-- 作者、专业、标准 -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                      <div class="flex items-center space-x-2 mb-1">
                        <div class="w-5 h-5 bg-green-500 rounded flex items-center justify-center">
                          <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                          </svg>
                        </div>
                        <span class="text-xs font-medium text-green-600 dark:text-green-400 uppercase">作者</span>
                      </div>
                      <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ mixedData.document_info.author }}</p>
                    </div>

                    <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                      <div class="flex items-center space-x-2 mb-1">
                        <div class="w-5 h-5 bg-purple-500 rounded flex items-center justify-center">
                          <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                          </svg>
                        </div>
                        <span class="text-xs font-medium text-purple-600 dark:text-purple-400 uppercase">专业</span>
                      </div>
                      <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ mixedData.document_info.major }}</p>
                    </div>

                    <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                      <div class="flex items-center space-x-2 mb-1">
                        <div class="w-5 h-5 bg-orange-500 rounded flex items-center justify-center">
                          <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                          </svg>
                        </div>
                        <span class="text-xs font-medium text-orange-600 dark:text-orange-400 uppercase">检测标准</span>
                      </div>
                      <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ mixedData.document_info.standard }}</p>
                    </div>
                  </div>
                </div>

                <!-- 右侧检测信息 -->
                <div class="lg:col-span-1">
                  <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600 text-center h-full flex flex-col justify-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-3">
                      <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                      </svg>
                    </div>

                    <div class="space-y-2">
                      <div class="text-center">
                        <div class="text-xs font-bold text-gray-900 dark:text-white">{{ getCheckedTime() }}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">检测时间</div>
                      </div>

                      <div class="flex justify-center">
                        <div :class="['inline-flex items-center px-2 py-1 rounded text-xs font-medium', getQualityLevelClass(mixedData.quality_level)]">
                          <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                          </svg>
                          {{ mixedData.quality_level }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 文档统计 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
            <div class="mb-4">
              <div class="flex items-center mb-1">
                <div class="w-9 h-9 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-bold text-gray-900 dark:text-white">文档统计</h2>
                  <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Document Statistics</p>
                </div>
              </div>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
              <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ mixedData.statistics.pages }}</div>
                <div class="text-xs text-blue-700 dark:text-blue-300 mt-1">页数</div>
              </div>
              <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ mixedData.statistics.words.toLocaleString() }}</div>
                <div class="text-xs text-green-700 dark:text-green-300 mt-1">字数</div>
              </div>
              <div class="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ mixedData.statistics.tables }}</div>
                <div class="text-xs text-purple-700 dark:text-purple-300 mt-1">表格数</div>
              </div>
              <div class="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div class="text-lg font-bold text-orange-600 dark:text-orange-400">{{ mixedData.statistics.images }}</div>
                <div class="text-xs text-orange-700 dark:text-orange-300 mt-1">图片数</div>
              </div>
              <div class="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div class="text-lg font-bold text-red-600 dark:text-red-400">{{ mixedData.statistics.formulas }}</div>
                <div class="text-xs text-red-700 dark:text-red-300 mt-1">公式数</div>
              </div>
              <div class="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div class="text-lg font-bold text-yellow-600 dark:text-yellow-400">{{ mixedData.statistics.spaces }}</div>
                <div class="text-xs text-yellow-700 dark:text-yellow-300 mt-1">空格数</div>
              </div>
              <div class="text-center p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                <div class="text-lg font-bold text-indigo-600 dark:text-indigo-400">{{ mixedData.statistics.footnotes }}</div>
                <div class="text-xs text-indigo-700 dark:text-indigo-300 mt-1">脚注数</div>
              </div>
              <div class="text-center p-3 bg-pink-50 dark:bg-pink-900/20 rounded-lg">
                <div class="text-lg font-bold text-pink-600 dark:text-pink-400">{{ mixedData.statistics.endnotes }}</div>
                <div class="text-xs text-pink-700 dark:text-pink-300 mt-1">尾注数</div>
              </div>
            </div>
          </div>

          <!-- 检测结果核心指标 -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <!-- 问题总数 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 relative overflow-hidden">
              <div class="absolute top-0 right-0 w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full -mr-10 -mt-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-red-500 dark:text-red-400" style="margin-top: 30px; margin-left: -30px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
              </div>
              <div class="relative z-10">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">问题总数</span>
                    <div class="relative ml-1 group">
                      <svg class="w-3 h-3 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="text-3xl font-bold text-red-600 dark:text-red-400 mb-1">{{ mixedData.total_errors }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">检测发现的所有问题</div>
              </div>
            </div>

            <!-- 严重错误 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 relative overflow-hidden">
              <div class="absolute top-0 right-0 w-20 h-20 bg-red-200 dark:bg-red-800/20 rounded-full -mr-10 -mt-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600 dark:text-red-400" style="margin-top: 30px; margin-left: -30px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="relative z-10">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">严重错误</span>
                    <div class="relative ml-1 group">
                      <svg class="w-3 h-3 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="text-3xl font-bold text-red-700 dark:text-red-400 mb-1">{{ mixedData.severity.critical }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">必须修改的错误</div>
              </div>
            </div>

            <!-- 一般错误 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 relative overflow-hidden">
              <div class="absolute top-0 right-0 w-20 h-20 bg-orange-100 dark:bg-orange-900/20 rounded-full -mr-10 -mt-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-500 dark:text-orange-400" style="margin-top: 30px; margin-left: -30px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="relative z-10">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">一般错误</span>
                    <div class="relative ml-1 group">
                      <svg class="w-3 h-3 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-1">{{ mixedData.severity.warning }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">建议修改的问题</div>
              </div>
            </div>

            <!-- 提醒事项 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 relative overflow-hidden">
              <div class="absolute top-0 right-0 w-20 h-20 bg-blue-100 dark:bg-blue-900/20 rounded-full -mr-10 -mt-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-500 dark:text-blue-400" style="margin-top: 30px; margin-left: -30px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="relative z-10">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">提醒</span>
                    <div class="relative ml-1 group">
                      <svg class="w-3 h-3 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">{{ mixedData.severity.suggestion }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">可选择性修改</div>
              </div>
            </div>
          </div>

          <!-- 问题详情和其他指标 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 问题详情 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
              <div class="mb-4">
                <div class="flex items-center mb-1">
                  <div class="w-9 h-9 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">问题详情</h2>
                    <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Problem Details</p>
                  </div>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div v-for="(category, index) in mixedData.categories" :key="index" 
                     class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div class="flex items-center">
                    <div :class="['w-6 h-6 rounded flex items-center justify-center mr-3', category.color]">
                      <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ category.name }}</span>
                  </div>
                  <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ category.count }} 种</span>
                </div>
              </div>
            </div>

            <!-- 其他指标 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
              <div class="mb-4">
                <div class="flex items-center mb-1">
                  <div class="w-9 h-9 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">其他指标</h2>
                    <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Other Metrics</p>
                  </div>
                </div>
              </div>
              <div class="space-y-4">
                <!-- 差错率 -->
                <div :class="['flex items-center justify-between p-4 rounded-lg', getErrorRateBgClass()]">
                  <div>
                    <div :class="['text-md font-medium', getErrorRateTextClass()]">差错率</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">问题密度×10000/总字符数（每万字差错率）</div>
                  </div>
                  <div class="text-right">
                    <div :class="['text-2xl font-bold', getErrorRateTextClass()]">{{ mixedData.error_rate }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">/10000</div>
                  </div>
                </div>
                
                <!-- 质量等级 -->
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">质量等级</span>
                  <div :class="['px-3 py-1 rounded-full text-sm font-medium', getQualityLevelClass(mixedData.quality_level)]">
                    {{ mixedData.quality_level }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 结构完整性分析 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
            <div class="mb-4">
              <div class="flex items-center mb-1">
                <div class="w-9 h-9 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-bold text-gray-900 dark:text-white">结构完整性分析</h2>
                  <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Structure Integrity Analysis</p>
                </div>
              </div>
            </div>
            
            <!-- 结构准确性总览 -->
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 flex items-center justify-between mb-4">
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">结构是否准确:</span>
                <span :class="['font-bold', mixedData.structure_accurate ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400']">
                  {{ mixedData.structure_accurate ? '是' : '否' }}
                </span>
              </div>
              <div class="flex items-center space-x-4">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-red-500 dark:text-red-400 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-gray-600 dark:text-gray-300">严重错误:</span>
                  <span class="font-semibold text-red-600 dark:text-red-400 ml-1">{{ mixedData.structure_critical }} 种</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-blue-500 dark:text-blue-400 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-gray-600 dark:text-gray-300">提醒:</span>
                  <span class="font-semibold text-blue-600 dark:text-blue-400 ml-1">{{ mixedData.structure_suggestion }} 种</span>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <!-- 左侧：标准结构 -->
              <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div class="flex items-center justify-center bg-teal-500 text-white py-2 rounded-t-lg mb-4">
                  <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                  </svg>
                  <span class="font-medium">标准结构</span>
                </div>
                <div class="space-y-2">
                  <!-- 当有标准结构数据时显示 -->
                  <div v-if="mixedData.standard_sections.length > 0" v-for="(section, index) in mixedData.standard_sections" :key="index"
                       class="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ section.name }}</span>
                    <span v-if="section.required" class="text-xs px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded">必需</span>
                    <span v-else class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded">可选</span>
                  </div>

                  <!-- 当无法获取标准结构数据时显示提示 -->
                  <div v-else class="flex items-center justify-center p-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div class="text-center">
                      <svg class="w-8 h-8 text-yellow-500 dark:text-yellow-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                      </svg>
                      <p class="text-sm text-yellow-700 dark:text-yellow-300 font-medium">无法获取检测标准配置</p>
                      <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">请检查网络连接或联系管理员</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧：当前论文结构 -->
              <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div class="bg-purple-500 text-white rounded-t-lg mb-4">
                  <div class="flex items-center justify-center py-2">
                    <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <span class="font-medium">当前论文结构</span>
                  </div>

                  <!-- 结构说明 -->
                  <div class="px-4 pb-3 text-xs text-purple-100">
                    <div class="grid grid-cols-2 gap-2">
                      <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-blue-300 rounded-full"></div>
                        <span>Word样式</span>
                      </div>
                      <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-purple-300 rounded-full"></div>
                        <span>居中识别</span>
                      </div>
                      <div class="flex items-center space-x-1">
                        <svg class="w-3 h-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <span>标准章节</span>
                      </div>
                      <div class="flex items-center space-x-1">
                        <svg class="w-3 h-3 text-purple-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <span>居中对齐</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="space-y-2">
                  <!-- 当有当前结构数据时显示 -->
                  <div v-if="mixedData.current_sections.length > 0" v-for="section in mixedData.current_sections" :key="`${section.name}-${section.paragraph_index || 'default'}`"
                       :class="[
                         'flex items-center justify-between p-3 rounded border',
                         section.type === 'actual'
                           ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                           : section.type === 'standard'
                           ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                           : section.type === 'non-standard'
                           ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
                           : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600'
                       ]">
                    <div class="flex items-center space-x-3 flex-1">
                      <!-- 类型指示器 -->
                      <div class="flex items-center space-x-1">
                        <!-- 实际标题的级别指示器 -->
                        <div v-if="section.type === 'actual' && section.level && section.level > 0" class="w-2 h-2 rounded-full" :class="{
                          'bg-blue-500': section.level === 1,
                          'bg-green-500': section.level === 2,
                          'bg-yellow-500': section.level === 3,
                          'bg-purple-500': section.level >= 4
                        }"></div>

                        <!-- 标准章节的图标 -->
                        <svg v-else-if="section.type === 'standard'" class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>

                        <!-- 类型标签 -->
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                          <span v-if="section.type === 'actual' && section.level && section.level > 0">H{{ section.level }}</span>
                          <span v-else-if="section.type === 'standard'">标准</span>
                          <span v-else-if="section.type === 'non-standard'">非标准</span>
                        </span>
                      </div>

                      <!-- 标题文本 -->
                      <div class="flex-1">
                        <span class="font-medium text-gray-900 dark:text-white" :class="{
                          'text-base': section.level === 1,
                          'text-sm': section.level === 2 || section.type === 'standard',
                          'text-xs': (section.level || 0) >= 3
                        }">{{ section.name }}</span>

                        <!-- 详细信息 -->
                        <div v-if="section.type === 'actual'" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <div class="flex items-center space-x-2">
                            <!-- 识别方式 -->
                            <span v-if="section.heading_type === 'style'" class="inline-flex items-center px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded text-xs">
                              Word样式
                            </span>
                            <span v-else-if="section.heading_type === 'centered'" class="inline-flex items-center px-1.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded text-xs">
                              居中识别
                            </span>

                            <!-- 对齐方式 -->
                            <span v-if="section.alignment === 'center'" class="inline-flex items-center text-purple-500">
                              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                              </svg>
                            </span>
                          </div>

                          <div class="mt-1">
                            <span v-if="section.style">样式: {{ section.style }}</span>
                            <span v-if="section.paragraph_index" :class="{'ml-2': section.style}">段落: {{ section.paragraph_index }}</span>
                          </div>
                        </div>
                        <div v-else-if="section.type === 'standard' && section.paragraph_index" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          位置: 段落 {{ section.paragraph_index }}
                        </div>
                      </div>
                    </div>

                    <!-- 状态指示器 -->
                    <div class="flex items-center space-x-2">
                      <span v-if="section.type === 'actual'" class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded">
                        Word标题
                      </span>
                      <span v-else-if="section.type === 'standard'" class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded">
                        识别章节
                      </span>
                      <span v-else-if="section.type === 'non-standard'" class="text-xs px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded">
                        非标准
                      </span>
                    </div>
                  </div>

                  <!-- 当无法获取当前结构数据时显示提示 -->
                  <div v-else class="flex items-center justify-center p-6 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div class="text-center">
                      <svg class="w-8 h-8 text-gray-400 dark:text-gray-500 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                      </svg>
                      <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">无法获取文档结构</p>
                      <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">文档可能未包含标题样式或分析失败</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 字数分析 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
            <div class="mb-4">
              <div class="flex items-center mb-1">
                <div class="w-9 h-9 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-bold text-gray-900 dark:text-white">字数分析</h2>
                  <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Word Count Analysis</p>
                </div>
              </div>
            </div>
            
            <!-- 字数达标性总览 -->
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 flex items-center justify-between mb-4">
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">字数是否达标:</span>
                <span :class="['font-bold', mixedData.word_count_compliant ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400']">
                  {{ mixedData.word_count_compliant ? '是' : '否' }}
                </span>
              </div>
              <div class="flex items-center space-x-4">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-red-500 dark:text-red-400 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-gray-600 dark:text-gray-300">严重错误:</span>
                  <span class="font-semibold text-red-600 dark:text-red-400 ml-1">{{ mixedData.word_count_errors }} 种</span>
                </div>
              </div>
            </div>
            
            <!-- 字数分析表格 -->
            <div class="overflow-x-auto">
              <table class="min-w-full">
                <thead class="bg-gray-50 dark:bg-gray-700/50">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">章节结构</th>
                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">标准要求</th>
                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">当前情况</th>
                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">分析结果</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                  <tr v-for="section in mixedData.word_analysis" :key="section.name" class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex items-center">
                        <div :class="['w-3 h-3 rounded-full mr-2', getSectionStatusColor(section.status)]"></div>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ section.name }}</span>
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-center">
                      <span class="text-sm text-gray-700 dark:text-gray-300">{{ section.standard }}</span>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-center">
                      <span class="text-sm text-gray-900 dark:text-white">{{ section.current }}</span>
                      <span v-if="section.unit" class="text-xs text-gray-500 dark:text-gray-400 ml-1">{{ section.unit }}</span>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-center">
                      <span :class="['inline-flex items-center px-2 py-1 text-xs font-medium rounded-full', getAnalysisResultClass(section.result)]">
                        <svg v-if="section.result === '达标'" class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <svg v-else-if="section.result === '不足'" class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        <svg v-else-if="section.result === '超标'" class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                        </svg>
                        <svg v-else class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                        </svg>
                        {{ section.result }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-between items-center">
            <button @click="goBack" 
                    class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
              </svg>
              返回基础报告
            </button>
            
            <div class="flex space-x-3">
              <button @click="exportReport" 
                      class="inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                导出PDF报告
              </button>
              <button @click="printReport" 
                      class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-1a2 2 0 00-2-2H9a2 2 0 00-2 2v1a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
                </svg>
                打印报告
              </button>
            </div>
          </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { authApi, taskApi } from '@/services'
import { systemApi } from '@/services/systemApi'
import type { SystemStats, ProblemStats, TaskDetail } from '@/types'

// 结构分析相关类型定义
interface StandardSection {
  name: string
  required: boolean
}

interface CurrentSection {
  name: string
  status: 'present' | 'missing' | 'extra'
  type: 'standard' | 'non-standard' | 'actual'
  level?: number
  style?: string
  paragraph_index?: number
  heading_type?: 'style' | 'centered' | 'unknown'  // 新增：标题识别方式
  alignment?: 'left' | 'center' | 'right' | 'justify'  // 新增：对齐方式
}

interface WordAnalysis {
  name: string
  standard: string
  current: string
  unit: string
  result: string
  status: 'present' | 'missing'
}

const router = useRouter()
const route = useRoute()

// 数据状态
const loading = ref(true)
const error = ref('')

// API数据
const systemStats = ref<SystemStats | null | undefined>(undefined)
const problemStats = ref<ProblemStats[]>([])
const userProfile = ref<any>(null)
const taskDetail = ref<TaskDetail | null>(null)

// 静态数据（无法从API获取的数据）
const staticData = ref({
  document_info: {
    title: '关于...',
    author: '...',
    major: '...',
    standard: '...',
    checked_at: ''
  },
  quality_level: '良好',
  error_rate: '0.68',
  structure_accurate: false,
  structure_critical: 2,
  structure_suggestion: 3,
  // 🔥 移除硬编码数据，将在fetchData中从API获取
  standard_sections: [] as StandardSection[],
  current_sections: [] as CurrentSection[],
  word_count_compliant: false,
  word_count_errors: 2,
  word_analysis: [] as WordAnalysis[]
})

// 混合数据计算属性
const mixedData = ref({
  document_id: route.params.id as string || 'c4a6c6fb-2b01-4381-8b79-fcbd616eea38',
  document_info: {
    title: '—',
    author: '—',
    major: '—',
    standard: '—',
    checked_at: '',
    degree_type: '学位论文' // 新增学位类型
  },
  quality_level: staticData.value.quality_level,
  error_rate: staticData.value.error_rate,
  total_errors: 0,
  statistics: {
    pages: 0,
    words: 0,
    tables: 0,
    images: 0,
    formulas: 0,
    spaces: 0,
    footnotes: 0,
    endnotes: 0
  },
  severity: {
    critical: 0,
    warning: 0,
    suggestion: 0
  },
  categories: [
    { name: '结构问题', count: 0, color: 'bg-red-500' },
    { name: '页面设置问题', count: 0, color: 'bg-orange-500' },
    { name: '字数问题', count: 0, color: 'bg-yellow-500' },
    { name: '段落问题', count: 0, color: 'bg-green-500' },
    { name: '内容问题', count: 0, color: 'bg-blue-500' },
    { name: '字体问题', count: 0, color: 'bg-purple-500' }
  ],
  structure_accurate: staticData.value.structure_accurate,
  structure_critical: staticData.value.structure_critical,
  structure_suggestion: staticData.value.structure_suggestion,
  standard_sections: staticData.value.standard_sections,
  current_sections: staticData.value.current_sections,
  word_count_compliant: staticData.value.word_count_compliant,
  word_count_errors: staticData.value.word_count_errors,
  word_analysis: staticData.value.word_analysis
})

// 获取真实数据
const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    // 并行获取各种数据
    const [
      systemStatsData,
      problemStatsData,
      userProfileData,
      taskDetailData
    ] = await Promise.allSettled([
      systemApi.getStats(),
      systemApi.getProblemStats(),
      authApi.getUserProfile(),
      route.params.id ? taskApi.getTask(route.params.id as string) : null
    ])
    
    // 处理系统统计数据
    if (systemStatsData.status === 'fulfilled' && systemStatsData.value) {
      systemStats.value = systemStatsData.value
      // 🔥 修复类型: 使用空值合并操作符提供默认值
      mixedData.value.total_errors = systemStatsData.value.total_problems ?? 0
    }
    
    // 处理问题统计数据
    if (problemStatsData.status === 'fulfilled') {
      problemStats.value = problemStatsData.value
      // 更新严重程度统计
      problemStatsData.value.forEach(stat => {
        if (stat.severity === 'critical') {
          mixedData.value.severity.critical = stat.count
        } else if (stat.severity === 'warning') {
          mixedData.value.severity.warning = stat.count
        } else if (stat.severity === 'info') {
          mixedData.value.severity.suggestion = stat.count
        }
      })
    }
    
    // 处理用户资料数据
    if (userProfileData.status === 'fulfilled') {
      userProfile.value = userProfileData.value
      // 更新用户相关信息
      if (userProfileData.value.full_name) {
        mixedData.value.document_info.author = userProfileData.value.full_name
      }
    }
    
    // 处理任务详情数据
    if (taskDetailData.status === 'fulfilled' && taskDetailData.value) {
      taskDetail.value = taskDetailData.value as any;
      
      // 🔥 修复类型：在使用 taskDetail.value 之前确保它不为 null
      if (taskDetail.value) {
        const cover = taskDetail.value.result?.document_info?.cover_page_info;
        // 论文题目
        mixedData.value.document_info.title = cover?.title || '—';
        // 作者
        mixedData.value.document_info.author = cover?.author || '—';
        // 专业
        mixedData.value.document_info.major = cover?.major || '—';
        // 学位类型
        mixedData.value.document_info.degree_type = cover?.degree_type || '学位论文'
        // 🔥 重构：直接使用后端返回的 standard_name 字段
        mixedData.value.document_info.standard = taskDetail.value.standard_name || '标准检测';
        // 检测时间
        mixedData.value.document_info.checked_at = taskDetail.value.created_at || '';
        // 文档编号
        mixedData.value.document_id = taskDetail.value.task_id || '—';
        
        // 🔥 重构: 使用更健壮的逻辑从任务结果中提取统计信息
        const taskResult = taskDetail.value.result || {};
        mixedData.value.statistics = {
          pages: getValueFromResult(taskResult, ['pages', 'page_count', 'total_pages'], 0),
          words: getValueFromResult(taskResult, ['words', 'word_count', 'total_words'], 0),
          tables: getValueFromResult(taskResult, ['tables', 'table_count'], 0),
          images: getValueFromResult(taskResult, ['images', 'image_count'], 0),
          formulas: getValueFromResult(taskResult, ['formulas', 'formula_count'], 0),
          spaces: getValueFromResult(taskResult, ['spaces', 'space_count'], 0),
          footnotes: getValueFromResult(taskResult, ['footnotes', 'footnote_count'], 0),
          endnotes: getValueFromResult(taskResult, ['endnotes', 'endnote_count'], 0),
        };

        // 🔥 新增：获取结构分析数据
        await fetchStructureAnalysis(taskResult);
      }
    }
    
    // 如果有些数据获取失败，使用默认值
    if (!systemStats.value) {
      mixedData.value.total_errors = 15
      mixedData.value.severity = { critical: 3, warning: 8, suggestion: 4 }
    }
    
    if (!mixedData.value.statistics.pages) {
      mixedData.value.statistics = {
        pages: 42,
        words: 28634,
        tables: 8,
        images: 12,
        formulas: 15,
        spaces: 1847,
        footnotes: 23,
        endnotes: 3
      }
    }
    
    console.log('统计报告数据加载完成:', {
      systemStats: systemStats.value,
      problemStats: problemStats.value,
      userProfile: userProfile.value,
      taskDetail: taskDetail.value,
      mixedData: mixedData.value
    })
    
  } catch (err) {
    console.error('获取统计数据失败:', err)
    error.value = '获取统计数据失败，请稍后重试。'
  } finally {
    loading.value = false
  }
}

// 🔥 新增：从 DocumentDetail.vue 移植过来的健壮的取值函数
const getValueFromResult = (result: any, keys: string[], defaultValue: any = 0): any => {
  if (!result) return defaultValue;
  
  const sources = [
    result,
    result.analysis_result,
    result.analysis_result?.content_stats,
    result.content_stats,
    result.document_info,
  ];

  for (const source of sources) {
    if (source) {
      for (const key of keys) {
        if (source[key] !== undefined && source[key] !== null) {
          return source[key];
        }
      }
    }
  }
  
  return defaultValue;
}

// 获取结构分析数据
const fetchStructureAnalysis = async (taskResult: any) => {
  try {
    // 1. 获取检测标准ID
    const detectionStandard = taskResult.detection_standard || 'hbkj_bachelor_2024'

    // 2. 从检测标准配置获取标准结构
    const standardSections = await getStandardSections(detectionStandard)
    mixedData.value.standard_sections = [...standardSections]

    // 3. 从任务结果获取当前文档结构
    const currentSections = getCurrentDocumentSections(taskResult)
    mixedData.value.current_sections = [...currentSections]

  } catch (error) {
    console.error('获取结构分析数据失败:', error)
    // 如果获取失败，设置为空数组
    mixedData.value.standard_sections = []
    mixedData.value.current_sections = getDefaultCurrentSections()
  }
}

// 🔥 新增：获取标准结构定义
const getStandardSections = async (detectionStandard: string): Promise<StandardSection[]> => {
  try {
    // 从API获取检测标准配置
    try {
      const response = await systemApi.getDetectionStandard(detectionStandard)

      // document_structure在definitions对象内部
      const documentStructure = (response as any)?.definitions?.document_structure

      if (documentStructure && Array.isArray(documentStructure)) {
        const sections = documentStructure.map((section: any) => ({
          name: section.name || section.title,
          required: section.required === true
        }))

        return sections
      }
    } catch (apiError: any) {
      console.error('从API获取检测标准失败，使用本地配置:', apiError)
    }

    // 如果API调用失败，返回空数组，让用户知道需要检查网络连接
    console.warn('无法从API获取检测标准配置，请检查网络连接')
    return []
  } catch (error) {
    console.error('获取标准结构失败:', error)
    return []
  }
}

// 从任务结果获取当前文档结构
const getCurrentDocumentSections = (taskResult: any): CurrentSection[] => {
  try {
    // 从任务结果的结构分析中获取实际的文档结构
    const analysisResult = taskResult.analysis_result
    const structureAnalysis = analysisResult?.structure_analysis

    console.log('分析结果:', analysisResult)
    console.log('结构分析:', structureAnalysis)

    const sections: CurrentSection[] = []

    // 1. 优先使用outline数据（真实的文档标题，基于Word样式）
    if (structureAnalysis?.outline && Array.isArray(structureAnalysis.outline)) {
      console.log('找到outline数据:', structureAnalysis.outline.length, '个标题')

      structureAnalysis.outline.forEach((item: any) => {
        sections.push({
          name: item.text || '未知标题',
          status: 'present' as const,
          type: 'actual' as const,
          level: item.level || 1,
          style: item.style || 'Normal',
          paragraph_index: item.paragraph_index,
          heading_type: item.type || 'unknown',  // 新增：标题识别方式
          alignment: item.alignment || 'left'    // 新增：对齐方式
        })
      })
    }

    // 2. 补充使用sections数据（识别的标准章节，基于identifiers匹配）
    if (structureAnalysis?.sections && Array.isArray(structureAnalysis.sections)) {
      console.log('找到sections数据:', structureAnalysis.sections.length, '个识别章节')

      structureAnalysis.sections.forEach((section: any) => {
        // 避免重复添加（如果outline中已经有相同的文本）
        const isDuplicate = sections.some(s =>
          s.name.toLowerCase().includes(section.text?.toLowerCase() || '') ||
          (section.text?.toLowerCase() || '').includes(s.name.toLowerCase())
        )

        if (!isDuplicate) {
          sections.push({
            name: section.name || section.text || '未知章节',
            status: 'present' as const,
            type: 'standard' as const,
            level: 0, // 标准章节没有级别概念
            style: 'Standard Section',
            paragraph_index: section.position || section.element_index
          })
        }
      })
    }

    // 3. 按段落位置排序
    sections.sort((a, b) => (a.paragraph_index || 0) - (b.paragraph_index || 0))

    console.log('最终提取到', sections.length, '个结构项目')
    return sections.length > 0 ? sections : getDefaultCurrentSections()

  } catch (error) {
    console.error('解析文档结构失败:', error)
    return getDefaultCurrentSections()
  }
}



// 默认当前结构（当无法从后端获取真实结构时使用）
const getDefaultCurrentSections = (): CurrentSection[] => {
  return []
}





// 页面展示时格式化检测时间
const getCheckedTime = () => {
  const t = mixedData.value.document_info.checked_at
  return t ? new Date(t).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }) : '—'
}

const getQualityLevelClass = (level: string) => {
  const classes: Record<string, string> = {
    '优秀': 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400',
    '良好': 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
    '一般': 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400',
    '较差': 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
  }
  return classes[level] || 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
}

const getErrorRateBgClass = () => {
  const rate = parseFloat(mixedData.value.error_rate)
  if (rate <= 0.25) return 'bg-gradient-to-r from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20'
  if (rate <= 0.5) return 'bg-gradient-to-r from-lime-50 to-green-100 dark:from-lime-900/20 dark:to-green-900/20'
  if (rate <= 1.0) return 'bg-gradient-to-r from-yellow-50 to-orange-100 dark:from-yellow-900/20 dark:to-orange-900/20'
  return 'bg-gradient-to-r from-red-50 to-pink-100 dark:from-red-900/20 dark:to-pink-900/20'
}

const getErrorRateTextClass = () => {
  const rate = parseFloat(mixedData.value.error_rate)
  if (rate <= 0.25) return 'text-green-700 dark:text-green-400'
  if (rate <= 0.5) return 'text-green-600 dark:text-green-400'
  if (rate <= 1.0) return 'text-orange-600 dark:text-orange-400'
  return 'text-red-600 dark:text-red-400'
}

const getSectionStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'present': 'bg-green-500',
    'missing': 'bg-red-500',
    'extra': 'bg-blue-500'
  }
  return colors[status] || 'bg-gray-500'
}

const getAnalysisResultClass = (result: string) => {
  const classes: Record<string, string> = {
    '达标': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    '不足': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    '超标': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
    '无要求': 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
  }
  return classes[result] || 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
}

// 操作函数
const goBack = () => {
  router.go(-1)
}

const exportReport = () => {
  alert('导出PDF报告功能开发中...')
}

const printReport = () => {
  window.print()
}

onMounted(() => {
  // 页面加载时获取数据
  fetchData()
})
</script>

<style scoped>
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}

/* 深色模式下的渐变背景 */
@media (prefers-color-scheme: dark) {
  .bg-gradient-to-br {
    --tw-gradient-from: #111827;
    --tw-gradient-to: #1f2937;
  }
}
</style> 