#!/usr/bin/env python3
"""
直接检查数据库中的任务结果
"""

import asyncio
import json
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select

# 导入模型
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.models.task import Task
from backend.app.core.config import settings

async def check_db_task():
    """检查数据库中的任务结果"""
    
    task_id = "task_5da7275afa414f2dbb9527c78a5fcbe5"
    
    # 创建数据库连接
    engine = create_async_engine(settings.database.url)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        # 查询任务
        stmt = select(Task).where(Task.task_id == task_id)
        result = await session.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            print(f"❌ 任务不存在: {task_id}")
            return
        
        print(f"✅ 找到任务: {task_id}")
        print(f"📊 任务状态: {task.status}")
        print(f"📊 任务进度: {task.progress}%")
        
        # 检查任务结果
        if task.result:
            print(f"\n📋 任务结果类型: {type(task.result)}")
            
            if isinstance(task.result, dict):
                result_dict = task.result
            else:
                try:
                    result_dict = json.loads(task.result)
                except:
                    print("❌ 无法解析任务结果为JSON")
                    return
            
            # 检查结构数据
            print(f"\n🔍 检查结果数据结构:")
            
            # 检查顶级字段
            document_structures = result_dict.get('document_structures', [])
            outline = result_dict.get('outline', [])
            print(f"  - 顶级 document_structures: {len(document_structures)} 个")
            print(f"  - 顶级 outline: {len(outline)} 个")
            
            # 检查analysis_result
            analysis_result = result_dict.get('analysis_result', {})
            if analysis_result:
                structure_analysis = analysis_result.get('structure_analysis', {})
                print(f"  - analysis_result.structure_analysis: {'存在' if structure_analysis else '不存在'}")
                if structure_analysis:
                    print(f"    - outline: {len(structure_analysis.get('outline', []))} 个")
                    print(f"    - sections: {len(structure_analysis.get('sections', []))} 个")
            
            # 保存原始结果
            with open('db_task_result.json', 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, ensure_ascii=False, indent=2)
            print(f"\n💾 数据库结果已保存到: db_task_result.json")
            
            # 显示所有顶级键
            print(f"\n📋 所有顶级键: {list(result_dict.keys())}")
            
        else:
            print("❌ 任务没有结果数据")

if __name__ == "__main__":
    print("🚀 开始检查数据库任务数据...")
    asyncio.run(check_db_task())
    print(f"\n🎉 检查完成！")
