"""
文档处理器

提供Word文档的处理功能：
- 文档打开、关闭和基本操作
- 文档内容提取和解析
- 文档信息获取
- 集成重试机制和线程安全
"""

import os
import time
import threading
import re
import json
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from pathlib import Path
import structlog

from app.services.word_com import get_word_application, WordCOMError
from app.core.resource_manager import get_word_pool
from app.core.retry import with_retry, WORD_OPERATION_RETRY_CONFIG
from app.core.threading import com_thread_safe
from app.core.config import settings
from dataclasses import dataclass, field

logger = structlog.get_logger()


@dataclass
class DocumentData:
    """封装文档所有分析数据，用于传递给规则引擎。"""
    file_path: str
    doc_info: Dict[str, Any] = field(default_factory=dict)
    content_stats: Dict[str, Any] = field(default_factory=dict)
    elements: List[Dict[str, Any]] = field(default_factory=list)
    paragraphs: List[Dict[str, Any]] = field(default_factory=list)
    tables: List[Dict[str, Any]] = field(default_factory=list)
    images: List[Dict[str, Any]] = field(default_factory=list)


class DocumentProcessorError(Exception):
    """文档处理器异常"""
    pass


class ChineseDateParser:
    """中文日期解析器"""
    
    # 中文数字映射
    CHINESE_NUMBER_MAP = {
        '零': 0, '○': 0, '〇': 0,
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5,
        '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10,
        '廿': 20, '卅': 30
    }
    
    # 中文月份映射
    CHINESE_MONTH_MAP = {
        '正': 1, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6,
        '七': 7, '八': 8, '九': 9, '十': 10, '冬': 11, '腊': 12
    }
    
    @classmethod
    def chinese_to_arabic(cls, chinese_str: str) -> int:
        """
        将中文数字转换为阿拉伯数字
        
        Args:
            chinese_str: 中文数字字符串
            
        Returns:
            int: 阿拉伯数字
        """
        if not chinese_str:
            return 0
            
        # 如果已经是阿拉伯数字，直接返回
        if chinese_str.isdigit():
            return int(chinese_str)
        
        # 特殊处理年份格式（如：二〇二五、二○二四等）
        if len(chinese_str) == 4 and all(c in cls.CHINESE_NUMBER_MAP for c in chinese_str):
            # 检查是否是年份格式（每个字符都是0-9的中文数字）
            year_digits = []
            for char in chinese_str:
                if char in cls.CHINESE_NUMBER_MAP:
                    digit = cls.CHINESE_NUMBER_MAP[char]
                    if digit <= 9:  # 只处理0-9的数字
                        year_digits.append(str(digit))
                    else:
                        # 不是年份格式，使用常规解析
                        break
            
            if len(year_digits) == 4:
                # 是年份格式，直接拼接
                return int(''.join(year_digits))
        
        # 常规中文数字转换
        result = 0
        temp_result = 0
        
        for char in chinese_str:
            if char in cls.CHINESE_NUMBER_MAP:
                num = cls.CHINESE_NUMBER_MAP[char]
                
                if num == 10:
                    # 处理十的情况
                    if temp_result == 0:
                        temp_result = 10
                    else:
                        temp_result *= 10
                elif num < 10:
                    # 处理个位数
                    temp_result += num
                else:
                    # 处理更大的数字单位
                    if temp_result == 0:
                        temp_result = 1
                    result += temp_result * num
                    temp_result = 0
        
        return result + temp_result
    
    @classmethod
    def parse_chinese_date(cls, date_str: str) -> Optional[str]:
        """
        解析中文日期格式
        
        Args:
            date_str: 中文日期字符串
            
        Returns:
            Optional[str]: 格式化后的日期字符串，如 "2025年4月20日"
        """
        if not date_str:
            return None
            
        logger.info(f"解析中文日期: {date_str}")
        
        # 定义各种中文日期格式的正则表达式
        date_patterns = [
            # 完整格式：二〇二五年四月二十日
            r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日',
            # 年月格式：二〇二五年四月
            r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月',
            # 简化格式：二五年四月二十日
            r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日',
            # 混合格式：2025年四月二十日
            r'(\d{4})年([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日',
            # 纯数字格式：2025年4月20日
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',
            # 纯数字格式：2025年4月
            r'(\d{4})年(\d{1,2})月',
            # 带空格的数字格式：2021 年 5月 16日
            r'(\d{4})\s+年\s+(\d{1,2})\s*月\s+(\d{1,2})\s*日',
            r'(\d{4})\s+年\s+(\d{1,2})\s*月',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                try:
                    groups = match.groups()
                    logger.info(f"匹配到日期模式: {pattern}, 组: {groups}")
                    
                    # 提取年
                    year_str = groups[0]
                    if year_str.isdigit():
                        year = int(year_str)
                    else:
                        year = cls.chinese_to_arabic(year_str)
                        # 处理简化年份（如二五年表示2025年）
                        if year < 100:
                            year += 2000
                    
                    # 提取月
                    month_str = groups[1]
                    if month_str.isdigit():
                        month = int(month_str)
                    else:
                        # 优先使用特殊月份映射
                        if month_str in cls.CHINESE_MONTH_MAP:
                            month = cls.CHINESE_MONTH_MAP[month_str]
                        else:
                            month = cls.chinese_to_arabic(month_str)
                    
                    # 提取日（如果有）
                    day = 1  # 默认为1日
                    if len(groups) > 2 and groups[2]:
                        day_str = groups[2]
                        if day_str.isdigit():
                            day = int(day_str)
                        else:
                            day = cls.chinese_to_arabic(day_str)
                    
                    # 验证日期有效性
                    if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31:
                        # 返回格式化的日期字符串
                        if len(groups) > 2 and groups[2]:
                            result = f"{year}年{month}月{day}日"
                        else:
                            result = f"{year}年{month}月"
                        
                        logger.info(f"成功解析中文日期: {date_str} -> {result}")
                        return result
                    else:
                        logger.warning(f"日期值超出有效范围: {year}年{month}月{day}日")
                        
                except Exception as e:
                    logger.error(f"解析中文日期失败: {date_str}, 错误: {str(e)}")
                    continue
        
        logger.warning(f"无法解析中文日期: {date_str}")
        return None


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self, use_pool: bool = True):
        """
        初始化文档处理器
        
        Args:
            use_pool: 是否使用Word实例池
        """
        self.use_pool = use_pool
        self.word_pool = get_word_pool() if use_pool else None
        self.processing_stats = {
            'documents_processed': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_processing_time': 0.0
        }
        self.date_parser = ChineseDateParser()
    
    @with_retry(WORD_OPERATION_RETRY_CONFIG)
    def get_document_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文档基本信息
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            dict: 文档信息
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始获取文档信息: {file_path}")
            
            # 验证文件
            self._validate_file(file_path)
            
            if self.use_pool and self.word_pool:
                # 使用实例池
                with self.word_pool.get_instance() as word_app:
                    doc = word_app.open_document(file_path, read_only=True)
                    try:
                        info = word_app.get_document_info(doc)
                        # 添加文件系统信息
                        info.update(self._get_file_system_info(file_path))
                        return info
                    finally:
                        word_app.close_document(doc)
            else:
                # 直接使用Word应用程序
                word_app = get_word_application()
                doc = word_app.open_document(file_path, read_only=True)
                try:
                    info = word_app.get_document_info(doc)
                    # 添加文件系统信息
                    info.update(self._get_file_system_info(file_path))
                    return info
                finally:
                    word_app.close_document(doc)
                    
        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            logger.error(f"获取文档信息失败: {file_path}, 错误: {str(e)}")
            raise DocumentProcessorError(f"获取文档信息失败: {str(e)}")
            
        finally:
            processing_time = time.time() - start_time
            self.processing_stats['total_processing_time'] += processing_time
            self.processing_stats['successful_operations'] += 1
            logger.info(f"获取文档信息完成: {file_path}, 耗时: {processing_time:.2f}秒")
    
    @with_retry(WORD_OPERATION_RETRY_CONFIG)
    def extract_document_content(self, file_path: str, include_formatting: bool = False) -> Dict[str, Any]:
        """
        提取文档内容
        
        Args:
            file_path: 文档文件路径
            include_formatting: 是否包含格式信息
            
        Returns:
            dict: 文档内容
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始提取文档内容: {file_path}")
            
            # 验证文件
            self._validate_file(file_path)
            
            if self.use_pool and self.word_pool:
                # 使用实例池
                with self.word_pool.get_instance() as word_app:
                    doc = word_app.open_document(file_path, read_only=True)
                    try:
                        content = self._extract_content_from_document(doc, include_formatting)
                        return content
                    finally:
                        word_app.close_document(doc)
            else:
                # 直接使用Word应用程序
                word_app = get_word_application()
                doc = word_app.open_document(file_path, read_only=True)
                try:
                    content = self._extract_content_from_document(doc, include_formatting)
                    return content
                finally:
                    word_app.close_document(doc)
                    
        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            logger.error(f"提取文档内容失败: {file_path}, 错误: {str(e)}")
            raise DocumentProcessorError(f"提取文档内容失败: {str(e)}")
            
        finally:
            processing_time = time.time() - start_time
            self.processing_stats['total_processing_time'] += processing_time
            self.processing_stats['documents_processed'] += 1
            logger.info(f"提取文档内容完成: {file_path}, 耗时: {processing_time:.2f}秒")
    
    @with_retry(WORD_OPERATION_RETRY_CONFIG)
    def analyze_document_structure(self, file_path: str) -> Dict[str, Any]:
        """
        分析文档结构
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            dict: 文档结构信息
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始分析文档结构: {file_path}")
            
            # 验证文件
            self._validate_file(file_path)
            
            if self.use_pool and self.word_pool:
                # 使用实例池
                with self.word_pool.get_instance() as word_app:
                    doc = word_app.open_document(file_path, read_only=True)
                    try:
                        structure = self._analyze_document_structure(doc)
                        return structure
                    finally:
                        word_app.close_document(doc)
            else:
                # 直接使用Word应用程序
                word_app = get_word_application()
                doc = word_app.open_document(file_path, read_only=True)
                try:
                    structure = self._analyze_document_structure(doc)
                    return structure
                finally:
                    word_app.close_document(doc)
                    
        except Exception as e:
            self.processing_stats['failed_operations'] += 1
            logger.error(f"分析文档结构失败: {file_path}, 错误: {str(e)}")
            raise DocumentProcessorError(f"分析文档结构失败: {str(e)}")
            
        finally:
            processing_time = time.time() - start_time
            self.processing_stats['total_processing_time'] += processing_time
            self.processing_stats['successful_operations'] += 1
            logger.info(f"分析文档结构完成: {file_path}, 耗时: {processing_time:.2f}秒")
    
    def _validate_file(self, file_path: str):
        """验证文件"""
        if not os.path.exists(file_path):
            raise DocumentProcessorError(f"文件不存在: {file_path}")
        
        if not os.path.isfile(file_path):
            raise DocumentProcessorError(f"路径不是文件: {file_path}")
        
        # 检查文件扩展名
        ext = os.path.splitext(file_path)[1].lower()
        if ext not in ['.doc', '.docx']:
            raise DocumentProcessorError(f"不支持的文件格式: {ext}")
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        max_size = getattr(settings, 'MAX_DOCUMENT_SIZE', 50 * 1024 * 1024)  # 默认50MB
        if file_size > max_size:
            raise DocumentProcessorError(f"文件过大: {file_size} bytes (最大: {max_size} bytes)")
    
    def _get_file_system_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件系统信息"""
        try:
            stat = os.stat(file_path)
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_size': stat.st_size,
                'file_extension': os.path.splitext(file_path)[1].lower(),
                'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'accessed_time': datetime.fromtimestamp(stat.st_atime).isoformat()
            }
        except Exception as e:
            logger.warning(f"获取文件系统信息失败: {str(e)}")
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_extension': os.path.splitext(file_path)[1].lower()
            }
    
    def _extract_content_from_document(self, doc: Any, include_formatting: bool = False) -> Dict[str, Any]:
        """从文档对象提取内容"""
        try:
            content = {
                'text': '',
                'paragraphs': [],
                'tables': [],
                'images': [],
                'headers_footers': {},
                'metadata': {}
            }
            
            # 提取文本内容
            try:
                content['text'] = doc.Content.Text
            except Exception as e:
                logger.warning(f"提取文档文本失败: {str(e)}")
            
            # 提取段落
            try:
                paragraphs = []
                for i, paragraph in enumerate(doc.Paragraphs):
                    para_info = {
                        'index': i + 1,
                        'text': paragraph.Range.Text.strip(),
                        'style': getattr(paragraph.Style, 'NameLocal', 'Normal') if hasattr(paragraph, 'Style') else 'Normal'
                    }
                    
                    if include_formatting:
                        para_info.update({
                            'alignment': getattr(paragraph.Format, 'Alignment', None),
                            'first_line_indent': getattr(paragraph.Format, 'FirstLineIndent', 0),
                            'left_indent': getattr(paragraph.Format, 'LeftIndent', 0),
                            'right_indent': getattr(paragraph.Format, 'RightIndent', 0),
                            'space_before': getattr(paragraph.Format, 'SpaceBefore', 0),
                            'space_after': getattr(paragraph.Format, 'SpaceAfter', 0)
                        })
                    
                    if para_info['text']:  # 只添加非空段落
                        paragraphs.append(para_info)
                
                content['paragraphs'] = paragraphs
                
            except Exception as e:
                logger.warning(f"提取段落信息失败: {str(e)}")
            
            # 提取表格
            try:
                tables = []
                for i, table in enumerate(doc.Tables):
                    table_info = {
                        'index': i + 1,
                        'rows': table.Rows.Count,
                        'columns': table.Columns.Count,
                        'data': []
                    }
                    
                    # 提取表格数据
                    try:
                        for row_idx in range(1, table.Rows.Count + 1):
                            row_data = []
                            for col_idx in range(1, table.Columns.Count + 1):
                                try:
                                    cell_text = table.Cell(row_idx, col_idx).Range.Text.strip()
                                    # 移除Word表格单元格末尾的特殊字符
                                    cell_text = cell_text.replace('\r\x07', '').replace('\x07', '')
                                    row_data.append(cell_text)
                                except Exception:
                                    row_data.append('')
                            table_info['data'].append(row_data)
                    except Exception as e:
                        logger.warning(f"提取表格数据失败: {str(e)}")
                    
                    tables.append(table_info)
                
                content['tables'] = tables
                
            except Exception as e:
                logger.warning(f"提取表格信息失败: {str(e)}")
            
            # 提取图片信息
            try:
                images = []
                for i, shape in enumerate(doc.InlineShapes):
                    if hasattr(shape, 'Type') and shape.Type == 3:  # wdInlineShapePicture
                        image_info = {
                            'index': i + 1,
                            'width': getattr(shape, 'Width', 0),
                            'height': getattr(shape, 'Height', 0),
                            'type': 'inline_picture'
                        }
                        images.append(image_info)
                
                # 检查浮动图片
                for i, shape in enumerate(doc.Shapes):
                    if hasattr(shape, 'Type') and shape.Type == 13:  # msoShapeTypePicture
                        image_info = {
                            'index': len(images) + 1,
                            'width': getattr(shape, 'Width', 0),
                            'height': getattr(shape, 'Height', 0),
                            'type': 'floating_picture'
                        }
                        images.append(image_info)
                
                content['images'] = images
                
            except Exception as e:
                logger.warning(f"提取图片信息失败: {str(e)}")
            
            # 提取页眉页脚
            try:
                headers_footers = {}
                
                # 页眉
                try:
                    for section_idx, section in enumerate(doc.Sections):
                        headers = {}
                        headers['primary'] = section.Headers(1).Range.Text.strip() if section.Headers(1).Exists else ''
                        headers['first_page'] = section.Headers(2).Range.Text.strip() if section.Headers(2).Exists else ''
                        headers['even_pages'] = section.Headers(3).Range.Text.strip() if section.Headers(3).Exists else ''
                        headers_footers[f'section_{section_idx + 1}_headers'] = headers
                except Exception as e:
                    logger.warning(f"提取页眉失败: {str(e)}")
                
                # 页脚
                try:
                    for section_idx, section in enumerate(doc.Sections):
                        footers = {}
                        footers['primary'] = section.Footers(1).Range.Text.strip() if section.Footers(1).Exists else ''
                        footers['first_page'] = section.Footers(2).Range.Text.strip() if section.Footers(2).Exists else ''
                        footers['even_pages'] = section.Footers(3).Range.Text.strip() if section.Footers(3).Exists else ''
                        headers_footers[f'section_{section_idx + 1}_footers'] = footers
                except Exception as e:
                    logger.warning(f"提取页脚失败: {str(e)}")
                
                content['headers_footers'] = headers_footers
                
            except Exception as e:
                logger.warning(f"提取页眉页脚失败: {str(e)}")
            
            return content
            
        except Exception as e:
            logger.error(f"提取文档内容失败: {str(e)}")
            raise DocumentProcessorError(f"提取文档内容失败: {str(e)}")
    
    def _analyze_document_structure(self, doc: Any) -> Dict[str, Any]:
        """分析文档结构"""
        try:
            structure = {
                'outline': [],
                'styles_used': [],
                'sections': [],
                'toc_entries': [],
                'statistics': {},
                'cover_page_info': {}  # 新增：封面页信息
            }
            
            # 基于规则检测文档结构
            try:
                document_structure = self._detect_document_structure_by_rules(doc)
                structure.update(document_structure)
            except Exception as e:
                logger.warning(f"基于规则的结构分析失败: {str(e)}")
                # 使用备用方法
                structure.update(self._fallback_structure_detection(doc))
            
            # 统计使用的样式
            try:
                styles_used = {}
                for paragraph in doc.Paragraphs:
                    style_name = getattr(paragraph.Style, 'NameLocal', 'Normal') if hasattr(paragraph, 'Style') else 'Normal'
                    styles_used[style_name] = styles_used.get(style_name, 0) + 1
                
                structure['styles_used'] = [
                    {'style': style, 'count': count}
                    for style, count in sorted(styles_used.items(), key=lambda x: x[1], reverse=True)
                ]
                
            except Exception as e:
                logger.warning(f"统计样式使用失败: {str(e)}")
            
            # 分析节信息
            try:
                sections = []
                for i, section in enumerate(doc.Sections):
                    section_info = {
                        'index': i + 1,
                        'page_setup': {
                            'page_width': getattr(section.PageSetup, 'PageWidth', 0),
                            'page_height': getattr(section.PageSetup, 'PageHeight', 0),
                            'top_margin': getattr(section.PageSetup, 'TopMargin', 0),
                            'bottom_margin': getattr(section.PageSetup, 'BottomMargin', 0),
                            'left_margin': getattr(section.PageSetup, 'LeftMargin', 0),
                            'right_margin': getattr(section.PageSetup, 'RightMargin', 0)
                        }
                    }
                    sections.append(section_info)
                
                structure['sections'] = sections
                
            except Exception as e:
                logger.warning(f"分析节信息失败: {str(e)}")
            
            # 获取统计信息
            try:
                statistics = {
                    # 使用Word内置的ComputeStatistics功能，这与Word UI的统计结果一致
                    # WdStatistic.wdStatisticParagraphs 的枚举值为 4
                    'paragraphs': doc.ComputeStatistics(4),  # 4 = wdStatisticParagraphs
                    'tables': doc.Tables.Count,
                    'images': len([s for s in doc.InlineShapes if hasattr(s, 'Type') and s.Type == 3]), # 3 = wdInlineShapePicture
                    'sections': doc.Sections.Count,
                    'pages': doc.ComputeStatistics(2),  # 2 = wdStatisticPages
                    'words': doc.ComputeStatistics(0),  # 0 = wdStatisticWords
                    'characters': doc.ComputeStatistics(3),  # 3 = wdStatisticCharacters
                }
                structure['statistics'] = statistics
                
            except Exception as e:
                logger.warning(f"获取文档统计信息失败: {str(e)}")
            
            # 提取封面页信息
            try:
                cover_page_info = self._extract_cover_page_info(doc)
                structure['cover_page_info'] = cover_page_info
                
            except Exception as e:
                logger.warning(f"提取封面页信息失败: {str(e)}")
            
            return structure
            
        except Exception as e:
            logger.error(f"分析文档结构失败: {str(e)}")
            raise DocumentProcessorError(f"分析文档结构失败: {str(e)}")
    
    def _get_heading_level(self, style_name: str) -> int:
        """获取标题级别"""
        style_name = style_name.lower()
        
        # 中文标题样式
        if '标题' in style_name:
            for i in range(1, 10):
                if f'标题 {i}' in style_name or f'标题{i}' in style_name:
                    return i
            return 1
        
        # 英文标题样式
        if 'heading' in style_name:
            for i in range(1, 10):
                if f'heading {i}' in style_name or f'heading{i}' in style_name:
                    return i
            return 1
        
        return 0

    def _is_centered_title(self, paragraph: Any, text: str, paragraph_index: int = 0) -> bool:
        """
        判断段落是否为居中的标题

        Args:
            paragraph: Word段落对象
            text: 段落文本
            paragraph_index: 段落索引

        Returns:
            是否为居中标题
        """
        try:
            # 检查文本长度（标题通常较短，但不能太短）
            if len(text) > 100 or len(text) < 2:
                return False

            # 检查是否为居中对齐
            alignment = self._get_paragraph_alignment(paragraph)
            if alignment != 'center':
                return False

            # 检查字体大小（标题通常比正文大）
            font_size = self._get_paragraph_font_size(paragraph)
            if font_size and font_size > 12:  # 大于12号字体
                logger.info(f"检测到大字号居中文本: '{text}' (字号: {font_size})")

            # 1. 检查是否为论文结构关键词
            structure_keywords = [
                # 声明和授权
                '声明', '授权', '原创性声明', '版权使用授权',
                # 摘要和关键词
                '摘要', 'abstract', '关键词', 'keywords',
                # 目录和致谢
                '目录', 'contents', '致谢', '附录',
                # 参考文献
                '参考文献', 'references', '文献',
                # 引言和结论
                '引言', 'introduction', '结论', 'conclusion', '总结',
                # 学位论文相关
                '学位论文', '毕业论文', '学士学位论文', '硕士学位论文', '博士学位论文',
                # 报告相关
                '开题报告', '任务书', '中期报告'
            ]

            text_lower = text.lower()
            for keyword in structure_keywords:
                if keyword in text_lower:
                    logger.info(f"通过关键词识别居中标题: '{text}' (关键词: {keyword})")
                    return True

            # 2. 检查是否为章节编号格式
            import re
            chapter_patterns = [
                r'^第[一二三四五六七八九十\d]+章',  # 第X章
                r'^\d+\.?\s*[^\d]',  # 数字开头
                r'^[一二三四五六七八九十]+[、．.]',  # 中文数字开头
                r'^\d+\.\d+',  # 1.1格式
            ]

            for pattern in chapter_patterns:
                if re.match(pattern, text):
                    logger.info(f"通过章节格式识别居中标题: '{text}'")
                    return True

            # 3. 特殊情况：如果是居中且字体较大，可能是标题
            if font_size and font_size >= 14 and len(text) <= 30:
                logger.info(f"通过字体大小识别居中标题: '{text}' (字号: {font_size})")
                return True

            # 4. 如果在前几页且居中，可能是封面相关标题
            if paragraph_index <= 50 and len(text) <= 50:  # 前50个段落
                # 检查是否包含论文标题特征
                title_indicators = ['研究', '分析', '探讨', '影响', '应用', '发展', '创新', '技术']
                for indicator in title_indicators:
                    if indicator in text:
                        logger.info(f"通过论文标题特征识别居中标题: '{text}'")
                        return True

            return False

        except Exception as e:
            logger.warning(f"检查居中标题失败: {str(e)}")
            return False

    def _get_paragraph_alignment(self, paragraph: Any) -> str:
        """
        获取段落对齐方式

        Args:
            paragraph: Word段落对象

        Returns:
            对齐方式: 'left', 'center', 'right', 'justify'
        """
        try:
            # Word对齐常量
            # 0 = wdAlignParagraphLeft
            # 1 = wdAlignParagraphCenter
            # 2 = wdAlignParagraphRight
            # 3 = wdAlignParagraphJustify

            # 尝试多种方式获取对齐方式
            alignment = 0  # 默认左对齐

            # 方式1：直接访问ParagraphFormat.Alignment
            try:
                if hasattr(paragraph, 'ParagraphFormat'):
                    para_format = paragraph.ParagraphFormat
                    if hasattr(para_format, 'Alignment'):
                        alignment = para_format.Alignment
            except:
                pass

            # 方式2：通过Range访问
            try:
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'ParagraphFormat'):
                        para_format = range_obj.ParagraphFormat
                        if hasattr(para_format, 'Alignment'):
                            alignment = para_format.Alignment
            except:
                pass

            alignment_map = {
                0: 'left',
                1: 'center',
                2: 'right',
                3: 'justify'
            }

            return alignment_map.get(alignment, 'left')

        except Exception as e:
            logger.warning(f"获取段落对齐方式失败: {str(e)}")
            return 'left'

    def _get_paragraph_font_size(self, paragraph: Any) -> float:
        """
        获取段落字体大小

        Args:
            paragraph: Word段落对象

        Returns:
            字体大小（磅）
        """
        try:
            # 尝试多种方式获取字体大小
            font_size = None

            # 方式1：通过Range.Font.Size
            try:
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'Font'):
                        font = range_obj.Font
                        if hasattr(font, 'Size'):
                            font_size = font.Size
            except:
                pass

            # 方式2：通过第一个字符的格式
            try:
                if not font_size and hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if range_obj.Characters.Count > 0:
                        first_char = range_obj.Characters(1)
                        if hasattr(first_char, 'Font'):
                            font = first_char.Font
                            if hasattr(font, 'Size'):
                                font_size = font.Size
            except:
                pass

            return float(font_size) if font_size else None

        except Exception as e:
            logger.warning(f"获取段落字体大小失败: {str(e)}")
            return None

    def _is_paragraph_bold(self, paragraph: Any) -> bool:
        """
        检查段落是否为粗体

        Args:
            paragraph: Word段落对象

        Returns:
            是否为粗体
        """
        try:
            # 方式1：通过Range.Font.Bold
            try:
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if hasattr(range_obj, 'Font'):
                        font = range_obj.Font
                        if hasattr(font, 'Bold'):
                            return bool(font.Bold)
            except:
                pass

            # 方式2：通过第一个字符的格式
            try:
                if hasattr(paragraph, 'Range'):
                    range_obj = paragraph.Range
                    if range_obj.Characters.Count > 0:
                        first_char = range_obj.Characters(1)
                        if hasattr(first_char, 'Font'):
                            font = first_char.Font
                            if hasattr(font, 'Bold'):
                                return bool(font.Bold)
            except:
                pass

            return False

        except Exception as e:
            logger.warning(f"检查段落粗体失败: {str(e)}")
            return False

    def _detect_cover_page_structure(self, doc: Any) -> dict:
        """
        检测封面页结构

        Args:
            doc: Word文档对象

        Returns:
            封面页结构信息
        """
        try:
            cover_info = {
                'has_cover': False,
                'title_found': False,
                'author_found': False,
                'advisor_found': False,
                'school_found': False,
                'date_found': False,
                'cover_elements': [],
                'cover_end_paragraph': 30  # 默认封面结束段落
            }

            # 检查前50个段落，寻找封面结束标志
            paragraph_count = min(50, doc.Paragraphs.Count)
            cover_end_found = False

            for i in range(1, paragraph_count + 1):
                try:
                    paragraph = doc.Paragraphs(i)
                    text = paragraph.Range.Text.strip()

                    if not text or len(text) < 2:
                        continue

                    # 检查是否为封面结束标志（如"摘要"、"目录"、正文开始等）
                    if self._is_cover_end_marker(text):
                        cover_info['cover_end_paragraph'] = i - 1
                        cover_end_found = True
                        logger.info(f"检测到封面结束标志: '{text}' 在段落 {i}")
                        break

                    # 只在封面范围内检查封面元素
                    if i <= 30:  # 前30个段落作为封面候选区域
                        # 检查是否包含封面关键信息
                        if self._is_title_text(text):
                            cover_info['title_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'title',
                                'text': text,
                                'paragraph_index': i
                            })

                        elif self._is_author_text(text):
                            cover_info['author_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'author',
                                'text': text,
                                'paragraph_index': i
                            })

                        elif self._is_advisor_text(text):
                            cover_info['advisor_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'advisor',
                                'text': text,
                                'paragraph_index': i
                            })

                        elif self._is_school_text(text):
                            cover_info['school_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'school',
                                'text': text,
                                'paragraph_index': i
                            })

                        elif self._is_date_text(text):
                            cover_info['date_found'] = True
                            cover_info['cover_elements'].append({
                                'type': 'date',
                                'text': text,
                                'paragraph_index': i
                            })

                except Exception as e:
                    continue

            # 判断是否有封面
            found_elements = sum([
                cover_info['title_found'],
                cover_info['author_found'],
                cover_info['advisor_found'],
                cover_info['school_found'],
                cover_info['date_found']
            ])

            cover_info['has_cover'] = found_elements >= 3  # 至少找到3个关键元素

            if cover_info['has_cover']:
                logger.info(f"检测到封面页，包含 {found_elements} 个关键元素，封面结束于段落 {cover_info['cover_end_paragraph']}")

            return cover_info

        except Exception as e:
            logger.warning(f"检测封面页结构失败: {str(e)}")
            return {'has_cover': False, 'cover_elements': [], 'cover_end_paragraph': 0}

    def _is_title_text(self, text: str) -> bool:
        """检查是否为论文标题"""
        title_indicators = [
            '研究', '分析', '探讨', '影响', '应用', '发展', '创新', '技术',
            '系统', '方法', '设计', '实现', '优化', '策略', '模式', '理论'
        ]
        # 标题通常较长且包含研究相关词汇
        return (len(text) > 8 and len(text) < 100 and
                any(indicator in text for indicator in title_indicators))

    def _is_author_text(self, text: str) -> bool:
        """检查是否为作者信息"""
        author_patterns = [
            r'姓\s*名', r'学生姓名', r'作\s*者', r'学\s*生',
            r'姓名.*[：:]', r'学生.*[：:]'
        ]
        import re
        return any(re.search(pattern, text) for pattern in author_patterns)

    def _is_advisor_text(self, text: str) -> bool:
        """检查是否为指导教师信息"""
        advisor_patterns = [
            r'指导教师', r'导\s*师', r'指导老师', r'指导.*[：:]'
        ]
        import re
        return any(re.search(pattern, text) for pattern in advisor_patterns)

    def _is_school_text(self, text: str) -> bool:
        """检查是否为学校信息"""
        school_keywords = [
            '大学', '学院', '学校', '科技学院', '师范大学', '理工大学'
        ]
        return any(keyword in text for keyword in school_keywords)

    def _is_date_text(self, text: str) -> bool:
        """检查是否为日期信息"""
        import re
        date_patterns = [
            r'\d{4}年\d{1,2}月',  # 2024年5月
            r'二〇\d+年',  # 二〇二四年
            r'\d{4}-\d{1,2}-\d{1,2}',  # 2024-05-20
            r'\d{4}/\d{1,2}/\d{1,2}'   # 2024/05/20
        ]
        return any(re.search(pattern, text) for pattern in date_patterns)

    def _is_cover_end_marker(self, text: str) -> bool:
        """
        检查是否为封面结束标志

        Args:
            text: 段落文本

        Returns:
            是否为封面结束标志
        """
        # 封面结束标志（论文正文开始的标志）
        end_markers = [
            '摘要', 'abstract', '目录', 'contents',
            '学位论文原创性声明', '学位论文版权使用授权书',
            '声明', '授权书', '原创性声明', '版权使用授权',
            '绪论', '引言', 'introduction', '第一章', '第1章',
            '1.', '1 ', 'chapter 1', 'chapter1'
        ]

        text_lower = text.lower().strip()

        # 精确匹配或开头匹配
        for marker in end_markers:
            if text_lower == marker.lower() or text_lower.startswith(marker.lower()):
                return True

        # 检查章节编号格式
        import re
        chapter_patterns = [
            r'^第[一二三四五六七八九十\d]+章',  # 第X章
            r'^\d+\.?\s*[^\d]',  # 数字开头
            r'^[一二三四五六七八九十]+[、．.]',  # 中文数字开头
        ]

        for pattern in chapter_patterns:
            if re.match(pattern, text):
                return True

        return False

    def _get_content_heading_level(self, text: str) -> int:
        """
        根据内容特征判断标题级别

        Args:
            text: 标题文本

        Returns:
            标题级别
        """
        text_lower = text.lower()

        # 一级标题关键词（重要章节）
        level1_keywords = [
            '摘要', 'abstract', '目录', 'contents', '致谢', '附录',
            '参考文献', 'references', '声明', '授权', '第一章', '第二章',
            '第三章', '第四章', '第五章', '第六章', 'chapter'
        ]

        # 二级标题关键词
        level2_keywords = [
            '关键词', 'keywords', '引言', 'introduction', '结论', 'conclusion'
        ]

        for keyword in level1_keywords:
            if keyword in text_lower:
                return 1

        for keyword in level2_keywords:
            if keyword in text_lower:
                return 2

        # 检查章节编号
        import re
        if re.match(r'^第[一二三四五六七八九十\d]+章', text):
            return 1
        elif re.match(r'^\d+\.?\s*[^\d]', text):
            return 2
        elif re.match(r'^[一二三四五六七八九十]+[、．.]', text):
            return 3

        return 1  # 默认为一级标题
    
    def _extract_cover_page_info(self, doc: Any) -> Dict[str, Any]:
        """
        从文档第一页提取封面页信息
        
        Args:
            doc: Word文档对象
            
        Returns:
            Dict[str, Any]: 封面页信息
        """
        try:
            import re
            
            # 初始化封面页信息
            cover_info = {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': ''
            }
            
            # 获取第一页内容
            first_page_text = self._get_first_page_content(doc)
            cover_info['raw_text'] = first_page_text
            
            logger.info(f"第一页文本内容: {first_page_text[:200]}...")
            
            # 使用正则表达式提取信息
            # 🔥 修复：优先使用\r分割，如果没有\r再使用\n分割
            if '\r' in first_page_text:
                lines = first_page_text.split('\r')
            else:
                lines = first_page_text.split('\n')
            lines = [self._clean_text(line) for line in lines if line.strip()]
            
            # 提取学校名称
            for line in lines:
                if any(keyword in line for keyword in ['学院', '大学', '学校', '科技学院', '科技大学']):
                    # 过滤掉包含其他关键词的行
                    if not any(keyword in line for keyword in ['院系', '专业', '导师', '指导教师']):
                        cover_info['school'] = self._clean_text(line)
                        break
            
            # 提取学位论文类型
            for line in lines:
                if any(keyword in line for keyword in ['学位论文', '毕业论文', '硕士论文', '博士论文', '学士论文']):
                    cover_info['degree_type'] = self._clean_text(line)
                    break
            
            # 🔥 优化：提取论文标题
            # 方式1: 从"设计（论文）题目："后面提取
            title_match = re.search(r'设计\（论文\）题目[：:\s]*([^\r\n]+)', first_page_text)
            if title_match and title_match.group(1).strip():
                cover_info['title'] = self._clean_text(title_match.group(1).strip())
                logger.info(f"从'设计（论文）题目'提取标题: {cover_info['title']}")
            else:
                # 方式2: 查找可能的标题行
                for i, line in enumerate(lines):
                    # 跳过空行、太短的行、或明显不是标题的行
                    if not line or len(line) < 8 or len(line) > 100:
                        continue
                    
                    # 跳过包含特殊标识符的行
                    if any(keyword in line for keyword in [
                        '学院', '大学', '学校', '学位论文', '毕业论文', '毕业设计',
                        '姓名', '学号', '专业', '导师', '指导', '年', '月', '日',
                        '声明', '授权', '签名', '摘要', 'Abstract', '关键词'
                    ]):
                        continue
                    
                    # 跳过英文标题（如果后面紧跟着中文标题）
                    if re.search(r'^[A-Za-z\s]+$', line) and i + 1 < len(lines):
                        # 检查下一行是否是中文标题
                        next_line = lines[i + 1]
                        if next_line and re.search(r'[\u4e00-\u9fff]', next_line) and 8 <= len(next_line) <= 100:
                            continue  # 跳过英文标题，使用中文标题
                    
                    # 检查是否为标题（包含中文字符或有意义的英文内容）
                    if (re.search(r'[\u4e00-\u9fff]', line) or  # 包含中文字符
                        (re.search(r'[A-Za-z]', line) and len(line) > 15)):  # 或较长的英文标题
                        
                        # 进一步过滤：不包含冒号、不全是数字等
                        if not re.search(r'[：:\d]{3,}', line):
                            cover_info['title'] = line
                            logger.info(f"从行分析提取标题: {cover_info['title']} (行{i})")
                            break
            
            # 提取姓名
            name_patterns = [
                r'姓\s*名[：:\s]*([^\s\n]+)',
                r'学生姓名[：:\s]*([^\s\n]+)',
                r'作\s*者[：:\s]*([^\s\n]+)',
            ]
            
            for pattern in name_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['author'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取学号
            student_id_patterns = [
                r'学\s*号[：:\s]*([0-9]+)',
                r'学生学号[：:\s]*([0-9]+)',
                r'学生编号[：:\s]*([0-9]+)',
            ]
            
            for pattern in student_id_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['student_id'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取院系
            department_patterns = [
                r'院\s*系[：:\s]*([^\s\n]+)',
                r'学\s*院[：:\s]*([^\s\n]+)',
                r'系\s*别[：:\s]*([^\s\n]+)',
            ]
            
            for pattern in department_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['department'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取专业
            major_patterns = [
                r'专\s*业[：:\s]*([^\s\n]+)',
                r'所学专业[：:\s]*([^\s\n]+)',
                r'专业名称[：:\s]*([^\s\n]+)',
            ]
            
            for pattern in major_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['major'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取指导老师
            advisor_patterns = [
                r'指导[教师]*[：:\s]*([^\s\n]+)',
                r'导\s*师[：:\s]*([^\s\n]+)',
                r'指导老师[：:\s]*([^\s\n]+)',
            ]
            
            for pattern in advisor_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    cover_info['advisor'] = self._clean_text(match.group(1).strip())
                    break
            
            # 提取时间 - 使用改进的中文日期解析器
            date_patterns = [
                # 完整中文日期格式
                r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年\s*([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月\s*([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日',
                # 中文年月日格式
                r'([二三四五六七八九十一○〇零壹贰叁肆伍陆柒捌玖拾]+)年\s*([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月',
                # 混合格式：数字年份 + 中文月日
                r'(\d{4})\s*年\s*([一二三四五六七八九十正冬腊○〇零壹贰叁肆伍陆柒捌玖拾]+)月\s*([一二三四五六七八九十廿卅○〇零壹贰叁肆伍陆柒捌玖拾]+)日',
                # 纯数字格式
                r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',
                r'(\d{4})\s*年\s*(\d{1,2})\s*月',
                # 简化中文格式
                r'二[○〇零一二三四五六七八九十][一二三四五六七八九十]*年',
            ]
            
            extracted_date = None
            for pattern in date_patterns:
                match = re.search(pattern, first_page_text)
                if match:
                    raw_date = match.group(0).strip()
                    logger.info(f"匹配到原始日期: {raw_date}")
                    
                    # 使用中文日期解析器处理
                    parsed_date = self.date_parser.parse_chinese_date(raw_date)
                    if parsed_date:
                        extracted_date = parsed_date
                        logger.info(f"成功解析日期: {raw_date} -> {parsed_date}")
                        break
                    else:
                        # 如果解析失败，保留原始文本
                        extracted_date = self._clean_text(raw_date)
                        logger.warning(f"日期解析失败，保留原始文本: {raw_date}")
                        break
            
            if extracted_date:
                cover_info['date'] = extracted_date
            else:
                # 尝试查找任何包含年份的行作为备选
                for line in lines:
                    if re.search(r'(19|20)\d{2}', line) and ('年' in line or '月' in line or '日' in line):
                        cover_info['date'] = self._clean_text(line)
                        logger.info(f"备选日期提取: {line}")
                        break
            
            # 记录提取结果
            logger.info(f"封面页信息提取完成: {cover_info}")
            
            return cover_info
            
        except Exception as e:
            logger.error(f"提取封面页信息失败: {str(e)}")
            return {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': '',
                'error': str(e)
            }
    
    def _clean_text(self, text: str) -> str:
        """
        清理文本中的控制字符和多余空白
        
        Args:
            text: 待清理的文本
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ''
        
        import re
        
        # 移除控制字符 (包括 \u0007, \r, \n, \t 等)
        # 保留正常的空格（\u0020），但去除其他控制字符
        cleaned = re.sub(r'[\u0000-\u001F\u007F-\u009F]', '', text)
        
        # 将多个连续的空白字符替换为单个空格
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # 去除首尾空白
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _get_first_page_content(self, doc: Any) -> str:
        """
        获取文档第一页的文本内容
        
        Args:
            doc: Word文档对象
            
        Returns:
            str: 第一页的文本内容
        """
        try:
            # 方法1：通过页面范围获取第一页内容
            try:
                # 获取第一页的范围
                page_range = doc.Range(0, doc.ComputeStatistics(0))  # 从文档开始到第一页结束
                
                # 如果文档有多页，计算第一页的结束位置
                if doc.ComputeStatistics(2) > 1:  # 总页数大于1
                    # 通过段落位置估算第一页结束位置
                    total_chars = doc.Range().Characters.Count
                    estimated_first_page_end = min(2000, total_chars // 2)  # 估算第一页大约的字符数
                    
                    page_range = doc.Range(0, estimated_first_page_end)
                
                first_page_text = page_range.Text
                
                if first_page_text and len(first_page_text.strip()) > 50:
                    return first_page_text
                    
            except Exception as e:
                logger.warning(f"方法1获取第一页内容失败: {str(e)}")
            
            # 方法2：通过前几个段落获取第一页内容
            try:
                first_page_paragraphs = []
                char_count = 0
                max_chars = 3000  # 限制字符数以确保是第一页
                
                for paragraph in doc.Paragraphs:
                    para_text = paragraph.Range.Text
                    
                    # 检查是否超过预期的第一页字符数
                    if char_count + len(para_text) > max_chars:
                        break
                    
                    first_page_paragraphs.append(para_text)
                    char_count += len(para_text)
                    
                    # 如果累计段落数过多，可能已经超过第一页
                    if len(first_page_paragraphs) > 50:
                        break
                
                first_page_text = '\n'.join(first_page_paragraphs)
                
                if first_page_text and len(first_page_text.strip()) > 50:
                    return first_page_text
                    
            except Exception as e:
                logger.warning(f"方法2获取第一页内容失败: {str(e)}")
            
            # 方法3：获取文档前半部分内容作为备用
            try:
                full_text = doc.Range().Text
                if full_text:
                    # 取前2000个字符作为第一页内容
                    first_page_text = full_text[:2000]
                    return first_page_text
                    
            except Exception as e:
                logger.warning(f"方法3获取第一页内容失败: {str(e)}")
            
            # 如果所有方法都失败，返回空字符串
            logger.warning("所有方法都无法获取第一页内容")
            return ""
            
        except Exception as e:
            logger.error(f"获取第一页内容失败: {str(e)}")
            return ""
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        total_operations = self.processing_stats['successful_operations'] + self.processing_stats['failed_operations']
        success_rate = (self.processing_stats['successful_operations'] / total_operations * 100) if total_operations > 0 else 0
        avg_time = (self.processing_stats['total_processing_time'] / total_operations) if total_operations > 0 else 0
        
        return {
            'documents_processed': self.processing_stats['documents_processed'],
            'successful_operations': self.processing_stats['successful_operations'],
            'failed_operations': self.processing_stats['failed_operations'],
            'success_rate': round(success_rate, 2),
            'total_processing_time': round(self.processing_stats['total_processing_time'], 2),
            'avg_processing_time': round(avg_time, 2),
            'using_pool': self.use_pool
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.processing_stats = {
            'documents_processed': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_processing_time': 0.0
        }

    def _detect_document_structure_by_rules(self, doc: Any) -> dict:
        """
        基于规则文件检测文档结构

        Args:
            doc: Word文档对象

        Returns:
            文档结构信息
        """
        try:
            # 加载规则文件
            rules = self._load_structure_rules()
            if not rules:
                logger.warning("无法加载结构规则，使用默认检测方式")
                return self._fallback_structure_detection(doc)

            # 按页面分析文档内容
            pages_content = self._extract_pages_content(doc)

            # 按页面顺序检测所有结构（标准和非标准）
            detected_structures = self._detect_structures_by_page_order(pages_content, rules)

            # 生成传统的outline格式（为了兼容前端）
            outline = self._generate_outline_from_structures(detected_structures, doc)

            return {
                'document_structures': detected_structures,
                'outline': outline,
                'structure_analysis_method': 'rule_based'
            }

        except Exception as e:
            logger.error(f"基于规则的结构检测失败: {str(e)}")
            return self._fallback_structure_detection(doc)

    def _load_structure_rules(self) -> list:
        """加载结构检测规则"""
        try:
            # 规则文件路径
            rules_file = Path(__file__).parent.parent.parent / "config" / "rules" / "hbkj_bachelor_2024.json"

            if not rules_file.exists():
                logger.warning(f"规则文件不存在: {rules_file}")
                return []

            with open(rules_file, 'r', encoding='utf-8') as f:
                rules_data = json.load(f)

            # 提取document_structure部分
            document_structure = rules_data.get('definitions', {}).get('document_structure', [])
            logger.info(f"成功加载 {len(document_structure)} 个结构规则")

            return document_structure

        except Exception as e:
            logger.error(f"加载结构规则失败: {str(e)}")
            return []

    def _extract_pages_content(self, doc: Any) -> dict:
        """
        按页面提取文档内容

        Args:
            doc: Word文档对象

        Returns:
            页面内容字典 {page_number: {'paragraphs': [...], 'tables': [...]}}
        """
        try:
            pages_content = {}

            # 遍历所有段落，按页面分组
            for i in range(1, doc.Paragraphs.Count + 1):
                try:
                    paragraph = doc.Paragraphs(i)
                    text = paragraph.Range.Text.strip()

                    if not text:
                        continue

                    # 获取段落所在页面
                    page_number = self._get_paragraph_page_number(paragraph)

                    if page_number not in pages_content:
                        pages_content[page_number] = {
                            'paragraphs': [],
                            'tables': [],
                            'page_number': page_number
                        }

                    # 检查是否在表格中
                    is_in_table = self._is_paragraph_in_table(paragraph)

                    paragraph_info = {
                        'index': i,
                        'text': text,
                        'style': getattr(paragraph.Style, 'NameLocal', 'Normal') if hasattr(paragraph, 'Style') else 'Normal',
                        'alignment': self._get_paragraph_alignment(paragraph),
                        'font_size': self._get_paragraph_font_size(paragraph),
                        'is_bold': self._is_paragraph_bold(paragraph),
                        'is_in_table': is_in_table
                    }

                    if is_in_table:
                        pages_content[page_number]['tables'].append(paragraph_info)
                    else:
                        pages_content[page_number]['paragraphs'].append(paragraph_info)

                except Exception as e:
                    logger.warning(f"处理段落 {i} 失败: {str(e)}")
                    continue

            logger.info(f"成功提取 {len(pages_content)} 页内容")
            return pages_content

        except Exception as e:
            logger.error(f"按页面提取内容失败: {str(e)}")
            return {}

    def _detect_structures_by_page_order(self, pages_content: dict, rules: list) -> list:
        """
        按页面顺序检测所有结构（标准和非标准）

        Args:
            pages_content: 页面内容字典
            rules: 结构规则列表

        Returns:
            按页面顺序排列的结构列表
        """
        all_structures = []

        # 识别所有封面页（包括非第一页的封面页）
        cover_pages = self._identify_all_cover_pages(pages_content)
        logger.info(f"识别到的封面页: {cover_pages}")

        # 按页面顺序遍历
        for page_num in sorted(pages_content.keys()):
            page_data = pages_content[page_num]

            # 跳过非第一页的封面页
            if page_num in cover_pages and page_num != 1:
                logger.info(f"跳过非第一页的封面页: 第{page_num}页")
                continue

            # 在当前页面检测标准结构
            page_standard_structures = self._detect_standard_structures_on_page(
                page_num, page_data, rules
            )

            # 检查当前页面是否为封面页
            is_cover_page = page_num in cover_pages

            # 如果是封面页，只保留封面结构，不检测其他非标准结构
            if is_cover_page:
                logger.info(f"第{page_num}页为封面页，跳过非标准结构检测")
                page_non_standard_structures = []
            else:
                # 在当前页面检测非标准结构
                page_non_standard_structures = self._detect_non_standard_structures_on_page(
                    page_num, page_data, page_standard_structures
                )

            # 按段落索引排序当前页面的结构
            page_structures = page_standard_structures + page_non_standard_structures
            page_structures.sort(key=lambda x: x.get('content', {}).get('paragraph_index', 0))

            # 添加到总列表
            all_structures.extend(page_structures)

        # 添加缺失的必需标准结构
        missing_structures = self._find_missing_required_structures(all_structures, rules)
        all_structures.extend(missing_structures)

        logger.info(f"按页面顺序检测完成，共 {len(all_structures)} 个结构")
        return all_structures

    def _get_paragraph_page_number(self, paragraph: Any) -> int:
        """获取段落所在页面号"""
        try:
            # 通过段落的Range获取页面信息
            if hasattr(paragraph, 'Range'):
                range_obj = paragraph.Range
                if hasattr(range_obj, 'Information'):
                    # wdActiveEndPageNumber = 3
                    page_number = range_obj.Information(3)
                    return int(page_number) if page_number else 1
            return 1
        except Exception as e:
            logger.warning(f"获取段落页面号失败: {str(e)}")
            return 1

    def _is_paragraph_in_table(self, paragraph: Any) -> bool:
        """检查段落是否在表格中"""
        try:
            if hasattr(paragraph, 'Range'):
                range_obj = paragraph.Range
                if hasattr(range_obj, 'Information'):
                    # wdWithInTable = 12
                    in_table = range_obj.Information(12)
                    return bool(in_table)
            return False
        except Exception as e:
            return False

    def _detect_standard_structures_on_page(self, page_num: int, page_data: dict, rules: list) -> list:
        """
        在指定页面检测标准结构

        Args:
            page_num: 页面号
            page_data: 页面数据
            rules: 结构规则列表

        Returns:
            该页面的标准结构列表
        """
        page_structures = []
        paragraphs = page_data.get('paragraphs', [])

        for structure_rule in rules:
            structure_name = structure_rule.get('name')
            identifiers = structure_rule.get('identifiers', [])
            required = structure_rule.get('required', False)

            # 对于关键词，使用特殊的检测方法
            if structure_name in ["中文关键词", "英文关键词"]:
                found_content = self._find_keywords_on_page(page_num, page_data, structure_name)
                if found_content:
                    page_structures.append({
                        'name': structure_name,
                        'status': 'present',
                        'type': 'standard',
                        'page': page_num,
                        'content': found_content,
                        'identifiers_matched': identifiers,
                        'required': required
                    })
                    logger.info(f"检测到结构: {structure_name} (第{page_num}页)")
                continue

            # 检测其他标准结构
            for i, paragraph in enumerate(paragraphs):
                text = paragraph.get('text', '').strip()
                if not text:
                    continue

                # 检查是否匹配标识符
                if self._matches_structure_identifiers(text, identifiers, structure_name):
                    # 验证结构特征
                    if self._validate_structure_characteristics(paragraph, structure_name):
                        content = {
                            'text': text,
                            'paragraph_index': i,
                            'alignment': paragraph.get('alignment', 'left'),
                            'font_size': paragraph.get('font_size'),
                            'style': paragraph.get('style', 'Normal'),
                            'is_bold': paragraph.get('is_bold', False)
                        }

                        page_structures.append({
                            'name': structure_name,
                            'status': 'present',
                            'type': 'standard',
                            'page': page_num,
                            'content': content,
                            'identifiers_matched': identifiers,
                            'required': required
                        })
                        logger.info(f"检测到结构: {structure_name} (第{page_num}页)")
                        break  # 找到一个就够了

        return page_structures

    def _detect_non_standard_structures_on_page(self, page_num: int, page_data: dict,
                                               page_standard_structures: list) -> list:
        """
        在指定页面检测非标准结构

        Args:
            page_num: 页面号
            page_data: 页面数据
            page_standard_structures: 该页面已检测到的标准结构

        Returns:
            该页面的非标准结构列表
        """
        page_non_standard_structures = []
        paragraphs = page_data.get('paragraphs', [])

        # 获取已检测的标准结构的段落索引
        used_paragraph_indices = set()
        for structure in page_standard_structures:
            content = structure.get('content', {})
            paragraph_index = content.get('paragraph_index')
            if paragraph_index is not None:
                used_paragraph_indices.add(paragraph_index)

        # 检测非标准结构
        for i, paragraph in enumerate(paragraphs):
            # 跳过已被标准结构使用的段落
            if i in used_paragraph_indices:
                continue

            text = paragraph.get('text', '').strip()
            alignment = paragraph.get('alignment', 'left')
            font_size = paragraph.get('font_size')
            is_bold = paragraph.get('is_bold', False)

            # 检查是否符合非标准结构特征
            if self._is_non_standard_structure(text, alignment, font_size, is_bold):
                structure_name = self._generate_non_standard_structure_name(text)

                content = {
                    'text': text,
                    'paragraph_index': i,
                    'alignment': alignment,
                    'font_size': font_size,
                    'style': paragraph.get('style', 'Normal'),
                    'is_bold': is_bold
                }

                page_non_standard_structures.append({
                    'name': structure_name,
                    'status': 'present',
                    'type': 'non_standard',
                    'page': page_num,
                    'content': content,
                    'identifiers_matched': [],
                    'required': False
                })

                logger.info(f"检测到非标准结构: {structure_name} (第{page_num}页) - {text[:30]}...")

        return page_non_standard_structures

    def _find_keywords_on_page(self, page_num: int, page_data: dict, keyword_type: str) -> dict:
        """
        在指定页面查找关键词

        Args:
            page_num: 页面号
            page_data: 页面数据
            keyword_type: 关键词类型（中文关键词或英文关键词）

        Returns:
            关键词内容字典，如果未找到则返回None
        """
        paragraphs = page_data.get('paragraphs', [])

        if keyword_type == "中文关键词":
            patterns = [
                r'关键词[：:]\s*(.+)',
                r'关键字[：:]\s*(.+)',
                r'Keywords?\s*[：:]\s*(.+)',
                r'Key\s*words?\s*[：:]\s*(.+)'
            ]
        else:  # 英文关键词
            patterns = [
                r'Key\s*words?\s*[：:]\s*(.+)',
                r'Keywords?\s*[：:]\s*(.+)'
            ]

        import re
        for i, paragraph in enumerate(paragraphs):
            text = paragraph.get('text', '').strip()

            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    keywords_text = match.group(1).strip()

                    # 验证关键词内容
                    if keyword_type == "中文关键词" and self._contains_chinese(keywords_text):
                        return {
                            'text': text,
                            'paragraph_index': i,
                            'alignment': paragraph.get('alignment', 'left'),
                            'font_size': paragraph.get('font_size'),
                            'style': paragraph.get('style', 'Normal'),
                            'is_bold': paragraph.get('is_bold', False)
                        }
                    elif keyword_type == "英文关键词" and self._contains_english(keywords_text):
                        return {
                            'text': text,
                            'paragraph_index': i,
                            'alignment': paragraph.get('alignment', 'left'),
                            'font_size': paragraph.get('font_size'),
                            'style': paragraph.get('style', 'Normal'),
                            'is_bold': paragraph.get('is_bold', False)
                        }

        return None

    def _contains_chinese(self, text: str) -> bool:
        """
        检查文本是否包含中文字符

        Args:
            text: 要检查的文本

        Returns:
            是否包含中文字符
        """
        import re
        # 匹配中文字符的正则表达式
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))

    def _contains_english(self, text: str) -> bool:
        """
        检查文本是否包含英文字符

        Args:
            text: 要检查的文本

        Returns:
            是否包含英文字符
        """
        import re
        # 匹配英文字母的正则表达式
        english_pattern = re.compile(r'[a-zA-Z]+')
        return bool(english_pattern.search(text))

    def _find_missing_required_structures(self, detected_structures: list, rules: list) -> list:
        """
        查找缺失的必需结构

        Args:
            detected_structures: 已检测到的结构列表
            rules: 结构规则列表

        Returns:
            缺失的必需结构列表
        """
        missing_structures = []
        detected_names = {s['name'] for s in detected_structures if s['status'] == 'present'}

        for rule in rules:
            structure_name = rule.get('name')
            required = rule.get('required', False)

            if required and structure_name not in detected_names:
                missing_structures.append({
                    'name': structure_name,
                    'status': 'missing',
                    'type': 'standard',
                    'required': required
                })
                logger.warning(f"缺失必需结构: {structure_name}")

        return missing_structures

    def _find_structure_in_pages(self, pages_content: dict, identifiers: list, start_page: int, structure_name: str) -> tuple:
        """
        在页面中查找特定结构

        Args:
            pages_content: 页面内容字典
            identifiers: 结构标识符列表
            start_page: 开始查找的页面
            structure_name: 结构名称

        Returns:
            (found_page, found_content) 或 (None, None)
        """
        try:
            # 封面特殊处理：只检查第一页的所有内容
            if structure_name == "封面":
                return self._find_cover_page(pages_content, identifiers)

            # 其他结构按原逻辑查找
            for page_num in sorted(pages_content.keys()):
                if page_num < start_page:
                    continue

                page_data = pages_content[page_num]

                # 只检查非表格段落
                for paragraph in page_data['paragraphs']:
                    text = paragraph['text']
                    alignment = paragraph.get('alignment', 'left')
                    font_size = paragraph.get('font_size')

                    # 检查是否匹配结构标识符
                    if self._matches_structure_identifiers(text, identifiers, structure_name):
                        # 额外验证：检查是否符合结构特征
                        if self._validate_structure_characteristics(paragraph, structure_name):
                            logger.info(f"在第{page_num}页找到结构 '{structure_name}': {text[:50]}...")
                            return page_num, {
                                'text': text,
                                'paragraph_index': paragraph['index'],
                                'alignment': alignment,
                                'font_size': font_size,
                                'style': paragraph.get('style', 'Normal')
                            }

            return None, None

        except Exception as e:
            logger.error(f"查找结构 {structure_name} 失败: {str(e)}")
            return None, None

    def _find_cover_page(self, pages_content: dict, identifiers: list) -> tuple:
        """
        专门查找封面页（第一页）

        Args:
            pages_content: 页面内容字典
            identifiers: 封面标识符列表

        Returns:
            (found_page, found_content) 或 (None, None)
        """
        try:
            # 只检查第一页
            if 1 not in pages_content:
                logger.warning("第一页内容不存在")
                return None, None

            page_data = pages_content[1]

            # 收集第一页的所有文本内容
            all_text_parts = []
            representative_paragraph = None

            # 收集所有非表格段落的文本
            for paragraph in page_data['paragraphs']:
                text = paragraph['text'].strip()
                if text:
                    all_text_parts.append(text)
                    # 选择一个代表性段落（优先选择包含"学位论文"的）
                    if not representative_paragraph or '学位论文' in text:
                        representative_paragraph = paragraph

            # 合并所有文本进行检测
            combined_text = ' '.join(all_text_parts)

            # 使用封面检测逻辑
            if self._is_cover_page_content(combined_text, identifiers):
                # 选择最具代表性的文本作为返回内容
                if representative_paragraph:
                    display_text = representative_paragraph['text']
                else:
                    # 如果没有找到代表性段落，使用第一个非空段落
                    display_text = all_text_parts[0] if all_text_parts else "封面页"

                logger.info(f"在第1页找到结构 '封面': {display_text[:50]}...")

                return 1, {
                    'text': display_text,
                    'paragraph_index': representative_paragraph['index'] if representative_paragraph else 1,
                    'alignment': representative_paragraph.get('alignment', 'center') if representative_paragraph else 'center',
                    'font_size': representative_paragraph.get('font_size') if representative_paragraph else None,
                    'style': representative_paragraph.get('style', 'Normal') if representative_paragraph else 'Normal',
                    'combined_text': combined_text[:200] + '...' if len(combined_text) > 200 else combined_text
                }

            return None, None

        except Exception as e:
            logger.error(f"查找封面页失败: {str(e)}")
            return None, None

    def _matches_structure_identifiers(self, text: str, identifiers: list, structure_name: str) -> bool:
        """检查文本是否匹配结构标识符"""
        # 标准化文本：去除所有空格并转为小写
        text_normalized = ''.join(text.split()).lower()

        # 对于封面，使用特殊的检测逻辑
        if structure_name == "封面":
            return self._is_cover_page_content(text, identifiers)

        # 注意：关键词检测现在由 _find_keywords_in_document 方法独立处理
        # 这里不再处理关键词检测，避免重复逻辑

        # 对于其他结构，匹配任一标识符即可
        for identifier in identifiers:
            # 标准化标识符：去除所有空格并转为小写
            identifier_normalized = ''.join(identifier.split()).lower()
            if identifier_normalized in text_normalized:
                return True

        return False

    def _is_cover_page_content(self, text: str, identifiers: list) -> bool:
        """
        专门检测封面页内容

        Args:
            text: 文本内容
            identifiers: 封面标识符列表

        Returns:
            是否为封面内容
        """
        # 封面关键特征
        cover_features = [
            # 学校相关
            '河北科技学院', '科技学院', '大学', '学院',
            # 学位论文相关
            '学士学位论文', '学位论文', '毕业论文', '论文',
            # 学生信息相关
            '姓名', '学号', '院系', '专业', '指导教师', '学生',
            # 题目相关
            '题目', '研究', '分析', '影响', '技术'
        ]

        # 计算匹配的特征数量
        matched_features = sum(1 for feature in cover_features if feature in text)

        # 如果匹配的特征足够多，认为是封面
        if matched_features >= 3:
            return True

        # 检查是否包含规则中定义的标识符
        matched_identifiers = sum(1 for identifier in identifiers if identifier in text)

        # 至少匹配2个规则标识符
        return matched_identifiers >= 2



    def _find_keywords_in_document(self, pages_content: dict, structure_name: str) -> tuple:
        """
        在整个文档中独立查找关键词

        Args:
            pages_content: 页面内容字典
            structure_name: 结构名称（"中文关键词" 或 "英文关键词"）

        Returns:
            tuple: (页面号, 内容) 或 (None, None)
        """
        logger.info(f"开始全文搜索关键词: {structure_name}")

        # 定义关键词检测模式
        if structure_name == "中文关键词":
            # 中文关键词模式：关键词：xxx 或 关键字：xxx
            pattern = r'^(关键词|关键字)\s*[：:]\s*(.+)'
        elif structure_name == "英文关键词":
            # 英文关键词模式：Keywords: xxx 或 Key words: xxx
            pattern = r'^(keywords?|key\s+words?)\s*[：:]\s*(.+)'
        else:
            return None, None

        # 遍历所有页面查找关键词
        for page_num, page_data in pages_content.items():
            paragraphs = page_data.get('paragraphs', [])

            for paragraph in paragraphs:
                text = paragraph.get('text', '').strip()

                if not text:
                    continue

                # 使用正则表达式匹配关键词模式
                flags = re.IGNORECASE if structure_name == "英文关键词" else 0
                match = re.match(pattern, text, flags)

                if match:
                    keywords_content = match.group(2).strip()

                    # 确保关键词内容不为空且有实际内容
                    if keywords_content and len(keywords_content) > 2:
                        logger.info(f"在第{page_num}页找到{structure_name}: {text[:50]}...")
                        return page_num, text

        logger.warning(f"未找到{structure_name}")
        return None, None

    # 注意：此方法已被 _detect_structures_by_page_order 中的按页面检测逻辑替代
    # 保留此方法仅为兼容性，实际不再使用
    def _detect_non_standard_structures(self, pages_content: dict, detected_structures: list) -> list:
        """
        检测非标准结构（规则文件中未定义的结构）

        ⚠️ 已废弃：此方法已被新的按页面顺序检测逻辑替代

        检测标准：
        1. 文字居中对齐
        2. 字号大于正文（通常 >= 14pt）
        3. 文本长度适中（不是正文段落）
        4. 不与已检测的标准结构重复
        5. 排除非第一页的封面页

        Args:
            pages_content: 页面内容字典
            detected_structures: 已检测到的标准结构列表

        Returns:
            非标准结构列表
        """
        non_standard_structures = []

        # 获取已检测结构的页面和段落索引，避免重复
        detected_positions = set()
        for structure in detected_structures:
            if structure['status'] == 'present':
                page = structure.get('page')
                content = structure.get('content', {})
                paragraph_index = content.get('paragraph_index', 0)
                if page:
                    detected_positions.add((page, paragraph_index))

        # 识别所有封面页（包括非第一页的封面页）
        cover_pages = self._identify_all_cover_pages(pages_content)

        logger.info("开始检测非标准结构...")
        logger.info(f"识别到的封面页: {cover_pages}")

        # 遍历所有页面查找非标准结构
        for page_num, page_data in pages_content.items():
            # 跳过封面页（除了第一页，其他封面页完全跳过）
            if page_num in cover_pages and page_num != 1:
                logger.info(f"跳过非第一页的封面页: 第{page_num}页")
                continue

            paragraphs = page_data.get('paragraphs', [])

            for i, paragraph in enumerate(paragraphs):
                # 跳过已检测的标准结构
                if (page_num, i) in detected_positions:
                    continue

                text = paragraph.get('text', '').strip()
                alignment = paragraph.get('alignment', 'left')
                font_size = paragraph.get('font_size')
                is_bold = paragraph.get('is_bold', False)

                # 检查是否符合非标准结构特征
                if self._is_non_standard_structure(text, alignment, font_size, is_bold):
                    # 生成结构名称
                    structure_name = self._generate_non_standard_structure_name(text)

                    non_standard_structures.append({
                        'name': structure_name,
                        'status': 'present',
                        'type': 'non_standard',
                        'page': page_num,
                        'content': {
                            'text': text,
                            'paragraph_index': i,
                            'alignment': alignment,
                            'font_size': font_size,
                            'style': paragraph.get('style', 'Normal'),
                            'is_bold': is_bold
                        },
                        'identifiers_matched': [],
                        'required': False
                    })

                    logger.info(f"检测到非标准结构: {structure_name} (第{page_num}页) - {text[:30]}...")

        logger.info(f"检测到 {len(non_standard_structures)} 个非标准结构")
        return non_standard_structures

    def _identify_all_cover_pages(self, pages_content: dict) -> set:
        """
        识别文档中的所有封面页（包括非第一页的封面页）

        封面页特征：
        1. 包含学位论文标题关键词
        2. 包含学生信息（姓名、学号等）
        3. 包含学校、专业信息
        4. 包含日期信息
        5. 整体布局与第一页相似

        Args:
            pages_content: 页面内容字典

        Returns:
            封面页页码集合
        """
        cover_pages = set()

        # 第一页默认为封面页
        if 1 in pages_content:
            cover_pages.add(1)

        # 从第一页提取封面特征信息
        first_page_features = self._extract_cover_features(pages_content.get(1, {}))

        # 检查其他页面是否为封面页
        for page_num, page_data in pages_content.items():
            if page_num == 1:  # 跳过第一页
                continue

            # 提取当前页面特征
            current_page_features = self._extract_cover_features(page_data)

            # 计算与第一页的相似度
            similarity = self._calculate_cover_similarity(first_page_features, current_page_features)

            # 如果相似度超过阈值，认为是封面页
            # 提高阈值到85%，确保只有真正相同的封面页才被识别
            if similarity >= 0.85:  # 85%相似度阈值
                cover_pages.add(page_num)
                logger.info(f"识别到封面页: 第{page_num}页 (相似度: {similarity:.2f})")
            else:
                logger.debug(f"第{page_num}页相似度不足: {similarity:.2f} < 0.85")

        return cover_pages

    def _extract_cover_features(self, page_data: dict) -> dict:
        """
        从页面中提取封面特征

        Args:
            page_data: 页面数据

        Returns:
            封面特征字典
        """
        features = {
            'has_degree_title': False,      # 是否有学位论文标题
            'has_student_info': False,      # 是否有学生信息
            'has_school_info': False,       # 是否有学校信息
            'has_date_info': False,         # 是否有日期信息
            'has_thesis_title': False,      # 是否有论文题目
            'all_texts': [],                # 所有文本内容（用于精确匹配）
            'text_with_format': [],         # 文本及其格式信息
            'centered_texts': [],           # 居中文本列表
            'large_font_texts': [],         # 大字号文本列表
            'total_paragraphs': 0           # 总段落数
        }

        paragraphs = page_data.get('paragraphs', [])
        features['total_paragraphs'] = len(paragraphs)

        # 封面关键词
        degree_keywords = ['学士学位论文', '硕士学位论文', '博士学位论文', '毕业论文', '毕业设计']
        student_keywords = ['姓名', '学号', '学生', '专业', '班级']
        school_keywords = ['学院', '大学', '学校', '系', '院系']
        date_keywords = ['年', '月', '日', '时间', '日期']

        for paragraph in paragraphs:
            text = paragraph.get('text', '').strip()
            alignment = paragraph.get('alignment', 'left')
            font_size = paragraph.get('font_size', 0)

            if not text:
                continue

            # 收集所有文本内容
            features['all_texts'].append(text)

            # 收集文本及其格式信息
            features['text_with_format'].append({
                'text': text,
                'alignment': alignment,
                'font_size': font_size,
                'is_bold': paragraph.get('is_bold', False)
            })

            # 检查学位论文标题
            if any(keyword in text for keyword in degree_keywords):
                features['has_degree_title'] = True

            # 检查学生信息
            if any(keyword in text for keyword in student_keywords):
                features['has_student_info'] = True

            # 检查学校信息
            if any(keyword in text for keyword in school_keywords):
                features['has_school_info'] = True

            # 检查日期信息
            if any(keyword in text for keyword in date_keywords):
                features['has_date_info'] = True

            # 检查是否为论文题目（通常较长且居中）
            if alignment == 'center' and len(text) > 10 and len(text) < 50:
                features['has_thesis_title'] = True

            # 收集居中文本
            if alignment == 'center':
                features['centered_texts'].append(text)

            # 收集大字号文本
            if font_size and font_size >= 16:
                features['large_font_texts'].append(text)

        return features

    def _calculate_cover_similarity(self, features1: dict, features2: dict) -> float:
        """
        计算两个页面封面特征的相似度

        更严格的相似度计算：
        1. 文本内容完全匹配权重最高 (50%)
        2. 文本格式完全匹配权重较高 (30%)
        3. 关键特征匹配权重较低 (20%)

        Args:
            features1: 第一个页面的特征
            features2: 第二个页面的特征

        Returns:
            相似度分数 (0-1)
        """
        if not features1 or not features2:
            return 0.0

        score = 0.0
        total_weight = 0.0

        # 1. 文本内容完全匹配 (权重50%)
        text_similarity = self._calculate_exact_text_similarity(
            features1.get('all_texts', []),
            features2.get('all_texts', [])
        )
        text_weight = 0.50
        score += text_similarity * text_weight
        total_weight += text_weight

        # 2. 文本格式完全匹配 (权重30%)
        format_similarity = self._calculate_format_similarity(
            features1.get('text_with_format', []),
            features2.get('text_with_format', [])
        )
        format_weight = 0.30
        score += format_similarity * format_weight
        total_weight += format_weight

        # 3. 关键特征匹配 (权重20%)
        key_features = [
            ('has_degree_title', 0.05),     # 学位论文标题
            ('has_student_info', 0.05),     # 学生信息
            ('has_school_info', 0.03),      # 学校信息
            ('has_date_info', 0.03),        # 日期信息
            ('has_thesis_title', 0.04)      # 论文题目
        ]

        # 计算关键特征相似度
        for feature, weight in key_features:
            total_weight += weight
            if features1.get(feature, False) and features2.get(feature, False):
                score += weight

        return score / total_weight if total_weight > 0 else 0.0

    def _calculate_exact_text_similarity(self, texts1: list, texts2: list) -> float:
        """
        计算文本内容的精确匹配相似度

        Args:
            texts1: 第一个页面的文本列表
            texts2: 第二个页面的文本列表

        Returns:
            相似度分数 (0-1)
        """
        if not texts1 or not texts2:
            return 0.0

        # 清理文本
        clean_texts1 = [text.strip() for text in texts1 if text.strip()]
        clean_texts2 = [text.strip() for text in texts2 if text.strip()]

        if not clean_texts1 or not clean_texts2:
            return 0.0

        # 计算完全匹配的文本数量
        set1 = set(clean_texts1)
        set2 = set(clean_texts2)

        # 交集 / 并集
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _calculate_format_similarity(self, format_data1: list, format_data2: list) -> float:
        """
        计算文本格式的相似度

        Args:
            format_data1: 第一个页面的格式数据
            format_data2: 第二个页面的格式数据

        Returns:
            相似度分数 (0-1)
        """
        if not format_data1 or not format_data2:
            return 0.0

        # 创建格式签名
        def create_format_signature(format_data):
            signatures = []
            for item in format_data:
                text = item.get('text', '').strip()
                if text:
                    signature = (
                        text,
                        item.get('alignment', 'left'),
                        item.get('font_size', 0),
                        item.get('is_bold', False)
                    )
                    signatures.append(signature)
            return set(signatures)

        sig1 = create_format_signature(format_data1)
        sig2 = create_format_signature(format_data2)

        if not sig1 or not sig2:
            return 0.0

        # 计算格式签名的相似度
        intersection = len(sig1.intersection(sig2))
        union = len(sig1.union(sig2))

        return intersection / union if union > 0 else 0.0

    def _calculate_text_list_similarity(self, list1: list, list2: list) -> float:
        """
        计算两个文本列表的相似度

        Args:
            list1: 第一个文本列表
            list2: 第二个文本列表

        Returns:
            相似度分数 (0-1)
        """
        if not list1 or not list2:
            return 0.0

        # 计算交集
        set1 = set(text.strip() for text in list1 if text.strip())
        set2 = set(text.strip() for text in list2 if text.strip())

        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _is_non_standard_structure(self, text: str, alignment: str, font_size: float, is_bold: bool) -> bool:
        """
        判断是否为非标准结构

        Args:
            text: 文本内容
            alignment: 对齐方式
            font_size: 字号
            is_bold: 是否粗体

        Returns:
            是否为非标准结构
        """
        # 基本条件检查
        if not text or len(text.strip()) == 0:
            return False

        # 过滤掉过长的文本（可能是正文段落）
        if len(text) > 100:
            return False

        # 过滤掉过短的文本（可能是页码等）
        if len(text) < 2:
            return False

        # 过滤掉纯数字或特殊字符
        if text.isdigit() or text in ['\r', '\n', '\t', ' ']:
            return False

        # 检查格式特征
        is_centered = alignment == 'center'
        is_large_font = font_size and font_size >= 14.0
        is_title_like = self._is_title_like_text(text)

        # 满足以下条件之一即认为是非标准结构：
        # 1. 居中且字号较大
        # 2. 居中且粗体
        # 3. 字号很大（>=16pt）
        # 4. 具有标题特征且居中
        return (
            (is_centered and is_large_font) or
            (is_centered and is_bold) or
            (font_size and font_size >= 16.0) or
            (is_title_like and is_centered)
        )

    def _is_title_like_text(self, text: str) -> bool:
        """
        判断文本是否具有标题特征

        Args:
            text: 文本内容

        Returns:
            是否具有标题特征
        """
        # 标题关键词
        title_keywords = [
            '第', '章', '节', '部分', '附件', '附表', '图', '表',
            '声明', '说明', '须知', '注意', '提示', '警告',
            '总结', '结论', '建议', '展望', '前言', '序言',
            '概述', '简介', '背景', '意义', '目的', '方法',
            '分析', '研究', '探讨', '讨论', '评价', '总结'
        ]

        # 检查是否包含标题关键词
        for keyword in title_keywords:
            if keyword in text:
                return True

        # 检查是否为章节编号格式
        import re
        chapter_patterns = [
            r'^第[一二三四五六七八九十\d]+章',
            r'^第[一二三四五六七八九十\d]+节',
            r'^[一二三四五六七八九十\d]+[、.]',
            r'^\d+\.\d+',
            r'^\(\d+\)',
            r'^[A-Z]\.',
        ]

        for pattern in chapter_patterns:
            if re.match(pattern, text.strip()):
                return True

        return False

    def _generate_non_standard_structure_name(self, text: str) -> str:
        """
        为非标准结构生成名称

        Args:
            text: 文本内容

        Returns:
            结构名称
        """
        # 清理文本
        clean_text = text.strip().replace('\r', '').replace('\n', '').replace('\t', ' ')

        # 如果文本较短，直接使用
        if len(clean_text) <= 20:
            return f"非标准结构: {clean_text}"

        # 如果文本较长，截取前20个字符
        return f"非标准结构: {clean_text[:20]}..."

    def _validate_structure_characteristics(self, paragraph: dict, structure_name: str) -> bool:
        """验证段落是否符合特定结构的特征"""
        text = paragraph['text']
        alignment = paragraph.get('alignment', 'left')
        font_size = paragraph.get('font_size')

        # 封面特征：通常居中，字体较大
        if structure_name == "封面":
            return True  # 封面检测已经在标识符匹配中处理

        # 标题类结构：通常居中对齐，字体较大
        title_structures = ["任务书", "开题报告", "诚信声明", "版权声明", "中文摘要", "英文摘要", "目录", "致谢", "附录"]
        if structure_name in title_structures:
            # 检查是否居中且字体较大，或者文本较短（可能是标题）
            is_centered = alignment == 'center'
            is_large_font = font_size and font_size >= 14
            is_short_text = len(text) <= 50

            return is_centered or is_large_font or is_short_text

        # 其他结构：基本匹配即可
        return True

    def _generate_outline_from_structures(self, detected_structures: list, doc: Any) -> list:
        """
        从检测到的结构生成outline格式

        Args:
            detected_structures: 检测到的结构列表
            doc: Word文档对象

        Returns:
            outline列表
        """
        outline = []

        for structure in detected_structures:
            if structure['status'] == 'present':
                content = structure.get('content', {})
                structure_type = structure.get('type', 'standard')

                outline_item = {
                    'paragraph_index': content.get('paragraph_index', 0),
                    'text': content.get('text', structure['name']),
                    'style': content.get('style', 'Normal'),
                    'level': self._get_structure_level(structure['name'], structure_type),
                    'type': structure_type,
                    'alignment': content.get('alignment', 'left'),
                    'page': structure.get('page', 1),
                    'structure_name': structure['name'],
                    'font_size': content.get('font_size'),
                    'is_bold': content.get('is_bold', False)
                }
                outline.append(outline_item)

        return outline

    def _get_structure_level(self, structure_name: str, structure_type: str = 'standard') -> int:
        """根据结构名称和类型获取标题级别"""

        # 标准结构的级别映射
        if structure_type == 'standard':
            level_map = {
                '封面': 0,
                '任务书': 1,
                '开题报告': 1,
                '诚信声明': 1,
                '版权声明': 1,
                '中文摘要': 1,
                '中文关键词': 2,
                '英文摘要': 1,
                '英文关键词': 2,
                '目录': 1,
                '正文': 1,
                '参考文献': 1,
                '致谢': 1,
                '附录': 1
            }
            return level_map.get(structure_name, 1)

        # 非标准结构的级别判断
        elif structure_type == 'non_standard':
            # 根据文本内容判断级别
            if '第' in structure_name and '章' in structure_name:
                return 1  # 章级别
            elif '第' in structure_name and '节' in structure_name:
                return 2  # 节级别
            elif any(keyword in structure_name for keyword in ['声明', '说明', '须知', '注意']):
                return 1  # 声明类
            else:
                return 2  # 默认为2级标题

        return 1

    def _fallback_structure_detection(self, doc: Any) -> dict:
        """
        备用的结构检测方法（使用原有逻辑）

        Args:
            doc: Word文档对象

        Returns:
            文档结构信息
        """
        try:
            logger.info("使用备用结构检测方法")

            # 使用原有的检测逻辑
            outline = []
            for i, paragraph in enumerate(doc.Paragraphs):
                text = paragraph.Range.Text.strip()
                if not text:
                    continue

                style_name = getattr(paragraph.Style, 'NameLocal', 'Normal') if hasattr(paragraph, 'Style') else 'Normal'

                # 检查是否为标题
                is_heading = False
                heading_level = 0
                heading_type = 'unknown'

                # 基于Word样式
                if '标题' in style_name or 'Heading' in style_name:
                    is_heading = True
                    heading_level = self._get_heading_level(style_name)
                    heading_type = 'style'

                # 基于居中对齐
                elif self._is_centered_title(paragraph, text, i + 1):
                    is_heading = True
                    heading_level = self._get_content_heading_level(text)
                    heading_type = 'centered'

                if is_heading:
                    outline_item = {
                        'paragraph_index': i + 1,
                        'text': text,
                        'style': style_name,
                        'level': heading_level,
                        'type': heading_type,
                        'alignment': self._get_paragraph_alignment(paragraph)
                    }
                    outline.append(outline_item)

            return {
                'outline': outline,
                'structure_analysis_method': 'fallback'
            }

        except Exception as e:
            logger.error(f"备用结构检测失败: {str(e)}")
            return {
                'outline': [],
                'structure_analysis_method': 'failed'
            }


# 全局文档处理器实例
_document_processor = None
_processor_lock = threading.Lock()


def get_document_processor() -> DocumentProcessor:
    """
    获取全局文档处理器实例
    
    Returns:
        DocumentProcessor: 文档处理器实例
    """
    global _document_processor
    
    with _processor_lock:
        if _document_processor is None:
            use_pool = getattr(settings, 'USE_WORD_POOL', True)
            _document_processor = DocumentProcessor(use_pool=use_pool)
        
        return _document_processor


def cleanup_document_processor():
    """清理全局文档处理器实例"""
    global _document_processor

    with _processor_lock:
        if _document_processor is not None:
            _document_processor = None
            logger.info("全局文档处理器实例已清理")