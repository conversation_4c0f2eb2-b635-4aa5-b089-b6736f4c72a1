#!/usr/bin/env python3
"""
检查任务结果中的文档结构数据
"""

import asyncio
import aiohttp
import json

async def check_task_result():
    """检查任务结果"""
    
    base_url = "http://localhost:8000"
    task_id = "task_5da7275afa414f2dbb9527c78a5fcbe5"
    username = "8966097"
    password = "heibailan5112"
    
    async with aiohttp.ClientSession() as session:
        # 1. 登录获取token
        print("🔐 正在登录...")
        login_data = {
            "username": username,
            "password": password
        }
        
        async with session.post(
            f"{base_url}/api/v1/auth/login",
            data=login_data
        ) as response:
            if response.status != 200:
                print(f"❌ 登录失败: {response.status}")
                return
            
            result = await response.json()
            data = result.get("data", {})
            access_token = data.get("access_token")
            
            if not access_token:
                print("❌ 未获取到访问令牌")
                return
            
            print("✅ 登录成功")
        
        # 2. 获取任务详情
        print(f"🔍 获取任务详情: {task_id}")
        headers = {
            "Authorization": f"Bearer {access_token}"
        }
        
        async with session.get(
            f"{base_url}/api/v1/tasks/{task_id}",
            headers=headers
        ) as response:
            if response.status != 200:
                print(f"❌ 获取任务失败: {response.status}")
                error_text = await response.text()
                print(f"错误信息: {error_text}")
                return
            
            task_data = await response.json()
            print("✅ 获取任务成功")
            
            # 检查响应结构
            if not task_data.get('success'):
                print("❌ API响应失败")
                print(f"响应: {task_data}")
                return
            
            task_info = task_data.get('data', {})
            result = task_info.get('result', {})
            
            print(f"📊 任务状态: {task_info.get('status')}")
            print(f"📊 任务进度: {task_info.get('progress')}%")
            
            # 检查文档结构数据
            if result:
                print("\n📋 检查结果数据结构:")
                
                # 检查document_structures
                document_structures = result.get('document_structures', [])
                print(f"  - document_structures: {len(document_structures)} 个")
                
                # 检查outline
                outline = result.get('outline', [])
                print(f"  - outline: {len(outline)} 个")
                
                # 检查analysis_result
                analysis_result = result.get('analysis_result', {})
                if analysis_result:
                    structure_analysis = analysis_result.get('structure_analysis', {})
                    if structure_analysis:
                        print(f"  - analysis_result.structure_analysis: 存在")
                        print(f"    - outline: {len(structure_analysis.get('outline', []))} 个")
                        print(f"    - sections: {len(structure_analysis.get('sections', []))} 个")
                
                # 显示前5个文档结构
                if document_structures:
                    print(f"\n📝 前5个文档结构:")
                    for i, structure in enumerate(document_structures[:5], 1):
                        name = structure.get('structure_name') or structure.get('name', 'Unknown')
                        type_info = structure.get('type', 'unknown')
                        page = structure.get('page', 'unknown')
                        status = structure.get('status', 'unknown')
                        print(f"  {i}. {name}")
                        print(f"     类型: {type_info}, 页面: {page}, 状态: {status}")
                elif outline:
                    print(f"\n📝 前5个大纲条目:")
                    for i, item in enumerate(outline[:5], 1):
                        name = item.get('structure_name') or item.get('text', 'Unknown')
                        level = item.get('level', 'unknown')
                        page = item.get('page', 'unknown')
                        print(f"  {i}. {name}")
                        print(f"     级别: {level}, 页面: {page}")
                
                # 保存完整结果到文件
                with open('task_result_new.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"\n💾 完整结果已保存到: task_result_new.json")
                
                # 检查前端需要的数据格式
                print(f"\n🔍 前端数据格式检查:")
                print(f"  - taskResult.document_structures: {'✅' if document_structures else '❌'}")
                print(f"  - taskResult.outline: {'✅' if outline else '❌'}")
                print(f"  - taskResult.analysis_result.structure_analysis: {'✅' if structure_analysis else '❌'}")
                
                return True
            else:
                print("❌ 任务没有结果数据")
                return False

if __name__ == "__main__":
    print("🚀 开始检查任务结构数据...")
    success = asyncio.run(check_task_result())
    
    if success:
        print(f"\n🎉 检查完成！")
        print(f"🔗 文档详情链接: http://localhost:3000/document-detail/task_5649eb787eb54608a774f4fe618f18ab")
    else:
        print(f"\n💥 检查失败！")
