"""
Word COM接口封装模块

提供Word应用程序的COM接口操作，包括：
- Word应用程序的启动和关闭
- 文档的打开、关闭和基本操作
- 线程安全的COM接口管理
- 资源清理和错误处理
"""

import os
import time
import threading
from typing import Optional, Dict, Any, List
from datetime import datetime
import structlog
import asyncio

try:
    import win32com.client
    import pythoncom
    from win32com.client import constants as word_constants
    COM_AVAILABLE = True
except ImportError:
    COM_AVAILABLE = False
    win32com = None
    pythoncom = None
    word_constants = None

from app.core.config import settings
from app.core.logging import logger


class WordCOMError(Exception):
    """Word COM操作异常"""
    pass


class WordApplication:
    """Word应用程序COM接口封装"""
    
    def __init__(self, visible: bool = False, timeout: int = 30):
        """
        初始化Word应用程序
        
        Args:
            visible: 是否显示Word界面
            timeout: 启动超时时间（秒）
        """
        if not COM_AVAILABLE:
            raise WordCOMError("pywin32库未安装，无法使用COM接口")
        
        self.visible = visible
        self.timeout = timeout
        self.word_app = None
        self.documents = {}  # 打开的文档缓存
        self.lock = threading.Lock()
        self.usage_count = 0
        self.max_usage_count = getattr(settings, 'WORD_MAX_USAGE_COUNT', 100)
        self.created_at = datetime.utcnow()
        
    def start(self) -> bool:
        """
        启动Word应用程序
        
        Returns:
            bool: 启动是否成功
        """
        try:
            with self.lock:
                if self.word_app is not None:
                    logger.warning("Word应用程序已经启动")
                    return True
                
                logger.info("正在启动Word应用程序...")
                start_time = time.time()
                
                # 初始化COM
                pythoncom.CoInitialize()
                
                # 创建Word应用程序实例
                self.word_app = win32com.client.Dispatch("Word.Application")
                
                # 配置Word应用程序
                self.word_app.Visible = self.visible
                self.word_app.DisplayAlerts = False  # 禁用警告对话框
                
                # 检查启动是否成功
                if self.word_app is None:
                    raise WordCOMError("无法创建Word应用程序实例")
                
                elapsed_time = time.time() - start_time
                logger.info(f"Word应用程序启动成功，耗时: {elapsed_time:.2f}秒")
                
                return True
                
        except Exception as e:
            logger.error(f"启动Word应用程序失败: {str(e)}")
            self.word_app = None
            raise WordCOMError(f"启动Word应用程序失败: {str(e)}")
    
    def stop(self) -> bool:
        """
        关闭Word应用程序
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            with self.lock:
                if self.word_app is None:
                    logger.warning("Word应用程序未启动")
                    return True
                
                logger.info("正在关闭Word应用程序...")
                
                # 关闭所有打开的文档
                self._close_all_documents()
                
                # 退出Word应用程序
                self.word_app.Quit()
                self.word_app = None
                
                # 清理COM
                pythoncom.CoUninitialize()
                
                logger.info("Word应用程序已关闭")
                return True
                
        except Exception as e:
            logger.error(f"关闭Word应用程序失败: {str(e)}")
            return False
    
    def is_running(self) -> bool:
        """
        检查Word应用程序是否正在运行
        
        Returns:
            bool: 是否正在运行
        """
        try:
            with self.lock:
                if self.word_app is None:
                    return False
                
                # 尝试访问Word应用程序属性
                _ = self.word_app.Name
                return True
                
        except Exception:
            logger.warning("Word应用程序连接已断开")
            self.word_app = None
            return False
    
    def open_document(self, file_path: str, read_only: bool = True) -> Optional[Any]:
        """
        打开Word文档
        
        Args:
            file_path: 文档文件路径
            read_only: 是否以只读模式打开
            
        Returns:
            Word文档对象
        """
        try:
            if not self.is_running():
                if not self.start():
                    raise WordCOMError("无法启动Word应用程序")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise WordCOMError(f"文件不存在: {file_path}")
            
            # 检查文件扩展名
            if not file_path.lower().endswith(('.docx', '.doc')):
                raise WordCOMError(f"不支持的文件格式: {file_path}")
            
            with self.lock:
                logger.info(f"正在打开文档: {file_path}")
                
                # 打开文档
                doc = self.word_app.Documents.Open(
                    FileName=file_path,
                    ReadOnly=read_only,
                    AddToRecentFiles=False,
                    Visible=False
                )
                
                if doc is None:
                    raise WordCOMError(f"无法打开文档: {file_path}")
                
                # 缓存文档对象
                doc_id = id(doc)
                self.documents[doc_id] = {
                    'document': doc,
                    'file_path': file_path,
                    'opened_at': datetime.utcnow(),
                    'read_only': read_only
                }
                
                self.usage_count += 1
                logger.info(f"文档打开成功: {file_path}, 使用次数: {self.usage_count}")
                
                return doc
                
        except Exception as e:
            logger.error(f"打开文档失败: {file_path}, 错误: {str(e)}")
            raise WordCOMError(f"打开文档失败: {str(e)}")
    
    def close_document(self, doc: Any, save_changes: bool = False) -> bool:
        """
        关闭Word文档
        
        Args:
            doc: Word文档对象
            save_changes: 是否保存更改
            
        Returns:
            bool: 关闭是否成功
        """
        try:
            if doc is None:
                return True
            
            with self.lock:
                doc_id = id(doc)
                doc_info = self.documents.get(doc_id)
                
                if doc_info:
                    file_path = doc_info['file_path']
                    logger.info(f"正在关闭文档: {file_path}")
                else:
                    logger.info("正在关闭文档")
                
                # 关闭文档
                doc.Close(SaveChanges=save_changes)
                
                # 从缓存中移除
                if doc_id in self.documents:
                    del self.documents[doc_id]
                
                logger.info("文档已关闭")
                return True
                
        except Exception as e:
            logger.error(f"关闭文档失败: {str(e)}")
            return False
    
    def get_document_info(self, doc: Any) -> Dict[str, Any]:
        """
        获取文档基本信息
        
        Args:
            doc: Word文档对象
            
        Returns:
            dict: 文档信息
        """
        try:
            if doc is None:
                raise WordCOMError("文档对象为空")
            
            # 获取文档属性
            built_in_props = doc.BuiltInDocumentProperties
            
            info = {
                'title': self._get_property_value(built_in_props, 'Title'),
                'author': self._get_property_value(built_in_props, 'Author'),
                'subject': self._get_property_value(built_in_props, 'Subject'),
                'keywords': self._get_property_value(built_in_props, 'Keywords'),
                'comments': self._get_property_value(built_in_props, 'Comments'),
                'created_date': self._get_property_value(built_in_props, 'Creation Date'),
                'modified_date': self._get_property_value(built_in_props, 'Last Save Time'),
                'pages': self._get_property_value(built_in_props, 'Number of Pages'),
                'words': self._get_property_value(built_in_props, 'Number of Words'),
                'characters': self._get_property_value(built_in_props, 'Number of Characters'),
                'paragraphs': self._get_property_value(built_in_props, 'Number of Paragraphs'),
                'file_name': doc.Name,
                'full_name': doc.FullName if hasattr(doc, 'FullName') else '',
            }
            
            # 获取统计信息
            try:
                stats = doc.ComputeStatistics
                info.update({
                    'stat_pages': stats(word_constants.wdStatisticPages),
                    'stat_words': stats(word_constants.wdStatisticWords),
                    'stat_characters': stats(word_constants.wdStatisticCharacters),
                    'stat_paragraphs': stats(word_constants.wdStatisticParagraphs),
                })
            except Exception as e:
                logger.warning(f"获取文档统计信息失败: {str(e)}")
            
            logger.info(f"获取文档信息成功: {info.get('file_name', 'Unknown')}")
            return info
            
        except Exception as e:
            logger.error(f"获取文档信息失败: {str(e)}")
            raise WordCOMError(f"获取文档信息失败: {str(e)}")
    
    def _get_property_value(self, properties, property_name: str) -> Any:
        """
        安全获取文档属性值
        
        Args:
            properties: 文档属性集合
            property_name: 属性名称
            
        Returns:
            属性值或None
        """
        try:
            return properties(property_name).Value
        except Exception:
            return None
    
    def _close_all_documents(self):
        """关闭所有打开的文档"""
        try:
            if self.word_app is None:
                return
            
            # 关闭缓存中的文档
            for doc_info in list(self.documents.values()):
                try:
                    doc = doc_info['document']
                    doc.Close(SaveChanges=False)
                except Exception as e:
                    logger.warning(f"关闭文档失败: {str(e)}")
            
            self.documents.clear()
            
            # 关闭Word应用程序中的所有文档
            try:
                for doc in self.word_app.Documents:
                    doc.Close(SaveChanges=False)
            except Exception as e:
                logger.warning(f"关闭Word应用程序文档失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"关闭所有文档失败: {str(e)}")
    
    def should_restart(self) -> bool:
        """
        检查是否需要重启Word应用程序
        
        Returns:
            bool: 是否需要重启
        """
        # 检查使用次数
        if self.usage_count >= self.max_usage_count:
            logger.info(f"Word应用程序使用次数达到上限: {self.usage_count}")
            return True
        
        # 检查运行时间（可选）
        uptime = (datetime.utcnow() - self.created_at).total_seconds()
        max_uptime = getattr(settings, 'WORD_MAX_UPTIME', 3600)  # 默认1小时
        if uptime > max_uptime:
            logger.info(f"Word应用程序运行时间过长: {uptime:.0f}秒")
            return True
        
        return False
    
    def restart(self) -> bool:
        """
        重启Word应用程序
        
        Returns:
            bool: 重启是否成功
        """
        try:
            logger.info("正在重启Word应用程序...")
            
            # 关闭当前应用程序
            self.stop()
            
            # 重置状态
            self.usage_count = 0
            self.created_at = datetime.utcnow()
            
            # 启动新的应用程序
            return self.start()
            
        except Exception as e:
            logger.error(f"重启Word应用程序失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取Word应用程序状态
        
        Returns:
            dict: 状态信息
        """
        return {
            'running': self.is_running(),
            'visible': self.visible,
            'usage_count': self.usage_count,
            'max_usage_count': self.max_usage_count,
            'open_documents': len(self.documents),
            'created_at': self.created_at.isoformat(),
            'uptime_seconds': (datetime.utcnow() - self.created_at).total_seconds(),
            'should_restart': self.should_restart()
        }


# 全局Word应用程序实例
_word_app_instance = None
_word_app_lock = threading.Lock()


def get_word_application() -> WordApplication:
    """
    获取全局Word应用程序实例（单例模式）
    
    Returns:
        WordApplication: Word应用程序实例
    """
    global _word_app_instance
    
    with _word_app_lock:
        if _word_app_instance is None:
            visible = getattr(settings, 'WORD_VISIBLE', False)
            timeout = getattr(settings, 'WORD_STARTUP_TIMEOUT', 30)
            _word_app_instance = WordApplication(visible=visible, timeout=timeout)
        
        # 检查是否需要重启
        if _word_app_instance.should_restart():
            logger.info("Word应用程序需要重启")
            _word_app_instance.restart()
        
        return _word_app_instance


def cleanup_word_application():
    """清理全局Word应用程序实例"""
    global _word_app_instance
    
    with _word_app_lock:
        if _word_app_instance is not None:
            _word_app_instance.stop()
            _word_app_instance = None
            logger.info("全局Word应用程序实例已清理")


async def get_com_status() -> Dict[str, Any]:
    """获取Word COM接口状态（用于健康检查）"""
    try:
        # 导入Word COM相关模块
        from app.services.word_application import WordApplication
        
        # 测试COM接口可用性
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 创建Word应用实例
            word_app = WordApplication()
            
            # 检查Word应用是否可用
            if hasattr(word_app, 'word_app') and word_app.word_app:
                # Word实例创建成功
                word_version = getattr(word_app.word_app, 'Version', 'Unknown')
                
                # 清理资源
                if hasattr(word_app, '_close_all_documents'):
                    word_app._close_all_documents()
                
                response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                
                return {
                    "status": "healthy",
                    "response_time_ms": round(response_time, 2),
                    "word_version": word_version,
                    "com_interface": "available",
                    "message": "Word COM接口正常"
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": "Word应用实例创建失败",
                    "message": "Word COM接口不可用"
                }
                
        except ImportError as e:
            return {
                "status": "unhealthy",
                "error": f"Word COM模块导入失败: {str(e)}",
                "message": "Word COM接口模块不可用"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": f"Word COM接口测试失败: {str(e)}",
                "message": "Word COM接口异常"
            }
            
    except Exception as e:
        logger.error(f"Word COM状态检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Word COM接口检查失败"
        }

async def test_word_com_basic() -> bool:
    """基础Word COM测试"""
    try:
        from app.services.word_application import WordApplication
        
        word_app = WordApplication()
        if hasattr(word_app, 'word_app') and word_app.word_app:
            # 清理资源
            if hasattr(word_app, '_close_all_documents'):
                word_app._close_all_documents()
            return True
        return False
        
    except Exception as e:
        logger.error(f"Word COM基础测试失败: {str(e)}")
        return False

async def get_word_com_info() -> Dict[str, Any]:
    """获取Word COM详细信息"""
    try:
        from app.services.word_application import WordApplication
        
        word_app = WordApplication()
        if hasattr(word_app, 'word_app') and word_app.word_app:
            info = {
                "version": getattr(word_app.word_app, 'Version', 'Unknown'),
                "build": getattr(word_app.word_app, 'Build', 'Unknown'),
                "name": getattr(word_app.word_app, 'Name', 'Microsoft Word'),
                "available": True
            }
            
            # 清理资源
            if hasattr(word_app, '_close_all_documents'):
                word_app._close_all_documents()
                
            return info
        else:
            return {"available": False}
            
    except Exception as e:
        logger.error(f"获取Word COM信息失败: {str(e)}")
        return {"available": False, "error": str(e)} 