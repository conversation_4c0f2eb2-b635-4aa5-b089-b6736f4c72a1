#!/usr/bin/env python3
"""
简化的文档结构测试
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_simple():
    """简单测试"""
    try:
        from app.services.document_processor import DocumentProcessor
        
        # 测试文档路径
        test_doc_path = project_root.parent / "docs" / "test.docx"
        
        if not test_doc_path.exists():
            print(f"❌ 测试文档不存在: {test_doc_path}")
            return False
        
        print(f"🔍 测试文档: {test_doc_path}")
        
        # 初始化处理器
        processor = DocumentProcessor()
        
        # 分析文档结构
        print("📊 开始分析文档结构...")
        structure = processor.analyze_document_structure(str(test_doc_path))
        
        # 检查结果
        outline = structure.get('outline', [])
        print(f"✅ 找到 {len(outline)} 个标题")
        
        # 统计类型
        style_count = sum(1 for item in outline if item.get('type') == 'style')
        centered_count = sum(1 for item in outline if item.get('type') == 'centered')
        center_aligned_count = sum(1 for item in outline if item.get('alignment') == 'center')
        
        print(f"📊 统计结果:")
        print(f"   - Word样式标题: {style_count}")
        print(f"   - 居中识别标题: {centered_count}")
        print(f"   - 居中对齐标题: {center_aligned_count}")
        
        # 显示前10个标题
        print(f"\n📝 前10个标题:")
        for i, item in enumerate(outline[:10], 1):
            text = item.get('text', '')[:30] + '...' if len(item.get('text', '')) > 30 else item.get('text', '')
            heading_type = item.get('type', 'unknown')
            alignment = item.get('alignment', 'unknown')
            level = item.get('level', 0)
            
            print(f"   {i:2d}. [{heading_type}] [{alignment}] H{level} | {text}")
        
        # 保存结果
        output_file = project_root / "simple_test_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 结果已保存到: {output_file}")
        
        return centered_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 简化文档结构测试")
    print("=" * 40)
    
    success = test_simple()
    
    if success:
        print("\n🎉 测试成功 - 居中标题识别正常工作")
    else:
        print("\n❌ 测试失败 - 居中标题识别需要调试")
