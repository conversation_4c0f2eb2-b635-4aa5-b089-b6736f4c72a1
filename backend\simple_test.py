#!/usr/bin/env python3
"""
简单测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("开始简单测试...")

try:
    from app.services.document_processor import DocumentProcessor
    print("成功导入DocumentProcessor")
    
    from app.core.logging import setup_logging
    print("成功导入setup_logging")
    
    # 设置日志
    setup_logging()
    print("日志设置完成")
    
    # 创建文档处理器
    processor = DocumentProcessor()
    print("文档处理器创建完成")
    
    # 测试文件路径
    test_file = project_root.parent / "docs" / "test.docx"
    print(f"测试文件路径: {test_file}")
    print(f"文件是否存在: {test_file.exists()}")
    
    if test_file.exists():
        print("开始分析文档...")
        result = processor.analyze_document_structure(str(test_file))
        print("分析完成")
        
        structures = result.get('document_structures', [])
        print(f"检测到 {len(structures)} 个结构")
        
        # 查找正文结构
        main_structures = [s for s in structures if s.get('name') == '正文']
        print(f"正文结构数量: {len(main_structures)}")
        
        for main in main_structures:
            print(f"正文: {main}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
