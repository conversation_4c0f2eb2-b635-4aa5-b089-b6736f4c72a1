#!/usr/bin/env python3
"""
测试非标准结构检测功能
"""

import sys
import os
sys.path.append('.')

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import WordInstancePool
import json

def test_non_standard_structure_detection():
    """测试非标准结构检测"""
    
    print("🧪 非标准结构检测测试")
    print("=" * 60)
    print("🔍 测试非标准结构检测功能")
    
    doc_path = r'D:\Works\paper-check-win\docs\test.docx'
    print(f"📄 测试文档: {doc_path}")
    print("=" * 60)
    
    # 初始化资源管理器和处理器
    resource_manager = WordInstancePool()
    processor = DocumentProcessor(resource_manager)
    
    try:
        # 分析文档结构
        print("📊 开始结构分析...")
        result = processor.analyze_document_structure(doc_path)
        
        print("\n🔧 1. 分析方法检查")
        print("-" * 30)
        analysis_method = result.get('structure_analysis_method', 'unknown')
        print(f"   分析方法: {analysis_method}")
        
        if analysis_method == 'rule_based':
            print("   ✅ 成功使用基于规则的分析方法")
        else:
            print("   ⚠️ 使用了备用分析方法")
        
        print("\n📋 2. 检测到的文档结构")
        print("-" * 30)
        
        structures = result.get('document_structures', [])
        if not structures:
            print("   ❌ 未检测到任何文档结构")
            return False
        
        # 分类统计
        standard_structures = [s for s in structures if s.get('type') == 'standard']
        non_standard_structures = [s for s in structures if s.get('type') == 'non_standard']
        
        print(f"   检测到 {len(structures)} 个结构:")
        print(f"   - 标准结构: {len(standard_structures)} 个")
        print(f"   - 非标准结构: {len(non_standard_structures)} 个")
        
        # 显示标准结构
        print("\n   📌 标准结构:")
        for i, structure in enumerate(standard_structures, 1):
            if structure['status'] == 'present':
                content = structure.get('content', {})
                text = content.get('text', structure['name'])
                page = structure.get('page', 'N/A')
                required = "必需" if structure.get('required', False) else "可选"
                print(f"      {i:2d}. ✅ {structure['name']} ({required}) [第{page}页]")
                print(f"          内容: {text[:50]}{'...' if len(text) > 50 else ''}")
            else:
                required = "必需" if structure.get('required', False) else "可选"
                print(f"      {i:2d}. ❌ {structure['name']} ({required})")
        
        # 显示非标准结构
        if non_standard_structures:
            print("\n   🎨 非标准结构:")
            for i, structure in enumerate(non_standard_structures, 1):
                content = structure.get('content', {})
                text = content.get('text', structure['name'])
                page = structure.get('page', 'N/A')
                alignment = content.get('alignment', 'unknown')
                font_size = content.get('font_size')
                is_bold = content.get('is_bold', False)
                
                print(f"      {i:2d}. 🎨 {structure['name']} [第{page}页]")
                print(f"          内容: {text}")
                print(f"          格式: 对齐={alignment}, 字号={font_size}, 粗体={is_bold}")
        else:
            print("\n   🎨 非标准结构: 无")
        
        print("\n📝 3. Outline兼容性检查")
        print("-" * 30)
        
        outline = result.get('outline', [])
        print(f"   Outline条目数: {len(outline)}")
        
        if outline:
            print("   前5个outline条目:")
            for i, item in enumerate(outline[:5], 1):
                item_type = item.get('type', 'unknown')
                level = item.get('level', 0)
                page = item.get('page', 'N/A')
                text = item.get('text', '')
                
                # 根据类型选择图标
                if item_type == 'standard':
                    icon = "📋"
                elif item_type == 'non_standard':
                    icon = "🎨"
                else:
                    icon = "📍"
                
                print(f"     {i}. {icon} H{level} [页{page}] | {text[:40]}{'...' if len(text) > 40 else ''}")
        
        print("\n📊 4. 非标准结构统计")
        print("-" * 30)
        
        if non_standard_structures:
            # 按页面分组统计
            page_stats = {}
            for structure in non_standard_structures:
                page = structure.get('page', 0)
                if page not in page_stats:
                    page_stats[page] = []
                page_stats[page].append(structure)
            
            print(f"   - 总计: {len(non_standard_structures)} 个非标准结构")
            print(f"   - 分布在: {len(page_stats)} 个页面")
            
            for page in sorted(page_stats.keys()):
                structures_on_page = page_stats[page]
                print(f"   - 第{page}页: {len(structures_on_page)} 个")
                for structure in structures_on_page:
                    content = structure.get('content', {})
                    text = content.get('text', '')[:30]
                    print(f"     * {text}...")
        else:
            print("   - 未检测到非标准结构")
        
        print("\n💾 5. 保存测试结果")
        print("-" * 30)
        
        # 保存结果到文件
        output_file = "non_standard_structure_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试结果已保存到: {os.path.abspath(output_file)}")
        
        print("\n📈 6. 测试结果评估")
        print("-" * 30)
        
        # 评估测试结果
        test_criteria = [
            ("使用了有效的分析方法", analysis_method == 'rule_based'),
            ("检测到文档结构", len(structures) > 0),
            ("检测到标准结构", len(standard_structures) > 0),
            ("检测到非标准结构", len(non_standard_structures) > 0),
            ("生成了outline", len(outline) > 0),
            ("非标准结构有格式信息", any(s.get('content', {}).get('font_size') for s in non_standard_structures))
        ]
        
        passed_tests = sum(1 for _, passed in test_criteria if passed)
        total_tests = len(test_criteria)
        
        print(f"测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        print()
        
        for criterion, passed in test_criteria:
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")
        
        print("\n" + "=" * 60)
        
        if passed_tests == total_tests:
            print("🎉 测试成功 - 非标准结构检测工作正常")
            return True
        else:
            print("❌ 测试失败 - 需要进一步优化")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_non_standard_structure_detection()
    sys.exit(0 if success else 1)
