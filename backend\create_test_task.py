#!/usr/bin/env python3
"""
创建测试任务，包含真实的文档结构数据
"""

import json
import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import get_db_session
from app.database import crud
from app.models.task import TaskCreate, TaskStatus, TaskType

async def create_test_task():
    """创建包含文档结构数据的测试任务"""
    
    # 读取真实的文档结构数据
    with open('document_structures_output.json', 'r', encoding='utf-8') as f:
        structure_data = json.load(f)
    
    # 构造任务结果数据
    task_result = {
        "document_structures": structure_data["document_structures"],
        "outline": structure_data["outline"],
        "analysis_result": {
            "structure_analysis": {
                "outline": structure_data["outline"],
                "sections": structure_data["document_structures"]
            }
        },
        "document_info": {
            "cover_page_info": {
                "title": "新媒体时代舞蹈编导创作手法的创新研究",
                "author": "张三",
                "major": "舞蹈编导",
                "degree_type": "学士学位论文"
            }
        },
        "detection_standard": "hbkj_bachelor_2024"
    }
    
    # 创建任务数据
    task_data = TaskCreate(
        task_id="test_structure_display_001",
        task_type=TaskType.PAPER_CHECK,
        file_path="D:/Works/paper-check-win/docs/test.docx",
        filename="test.docx",
        file_size=1024000,
        user_id="test_user_001",  # 添加用户ID
        analysis_options={
            "detection_standard": "hbkj_bachelor_2024",
            "check_structure": True
        },
        status=TaskStatus.COMPLETED,
        progress=100,
        result=task_result,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    # 保存到数据库
    async for session in get_db_session():
        try:
            # 检查是否已存在
            existing_task = await crud.get_task(session, "test_structure_display_001")
            if existing_task:
                print("任务已存在，更新任务结果...")
                # 更新现有任务的结果
                from app.models.task import TaskUpdate
                update_data = TaskUpdate(
                    status=TaskStatus.COMPLETED,
                    progress=100,
                    result=task_result
                )
                updated_task = await crud.update_task(session, "test_structure_display_001", update_data)
                print(f"✅ 测试任务更新成功: {updated_task.task_id}")
                print(f"📊 包含 {len(structure_data['document_structures'])} 个文档结构")
                print(f"🔗 访问链接: http://localhost:3001/statistics-report/{updated_task.task_id}")
                return updated_task.task_id
            else:
                # 创建新任务
                created_task = await crud.create_task(session, task_data)
                print(f"✅ 测试任务创建成功: {created_task.task_id}")
                print(f"📊 包含 {len(structure_data['document_structures'])} 个文档结构")
                print(f"🔗 访问链接: http://localhost:3001/statistics-report/{created_task.task_id}")
                return created_task.task_id

        except Exception as e:
            print(f"❌ 创建任务失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == "__main__":
    print("🚀 开始创建测试任务...")
    task_id = asyncio.run(create_test_task())
    
    if task_id:
        print(f"\n🎉 测试任务创建完成！任务ID: {task_id}")
    else:
        print("\n💥 测试任务创建失败！")
        sys.exit(1)
