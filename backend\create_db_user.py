#!/usr/bin/env python3
"""
直接在数据库中创建测试用户
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import get_db_session
from app.database import crud
from app.models.user import UserCreate
from app.security import get_password_hash

async def create_test_user():
    """在数据库中创建测试用户"""
    
    # 测试用户数据
    user_data = UserCreate(
        username="test_user",
        email="<EMAIL>",
        password="test123456",
        full_name="测试用户"
    )
    
    async for session in get_db_session():
        try:
            # 检查用户是否已存在
            existing_user = await crud.get_user_by_username(session, user_data.username)
            if existing_user:
                print(f"✅ 用户已存在: {user_data.username}")
                print(f"📧 邮箱: {existing_user.email}")
                print(f"👤 全名: {existing_user.full_name}")
                return existing_user.user_id
            
            # 创建新用户
            hashed_password = get_password_hash(user_data.password)
            created_user = await crud.create_user(session, user_data, hashed_password)
            print(f"✅ 测试用户创建成功: {created_user.username}")
            print(f"📧 邮箱: {created_user.email}")
            print(f"👤 全名: {created_user.full_name}")
            print(f"🆔 用户ID: {created_user.user_id}")

            return created_user.user_id
            
        except Exception as e:
            print(f"❌ 创建用户失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

async def update_task_user():
    """更新测试任务的用户ID"""
    
    async for session in get_db_session():
        try:
            # 获取测试用户
            user = await crud.get_user_by_username(session, "test_user")
            if not user:
                print("❌ 找不到测试用户")
                return False
            
            # 获取测试任务
            task = await crud.get_task(session, "test_structure_display_001")
            if not task:
                print("❌ 找不到测试任务")
                return False
            
            # 更新任务的用户ID
            from app.models.task import TaskUpdate
            update_data = TaskUpdate(user_id=user.user_id)
            updated_task = await crud.update_task(session, "test_structure_display_001", update_data)
            
            print(f"✅ 任务用户ID已更新: {updated_task.user_id}")
            return True
            
        except Exception as e:
            print(f"❌ 更新任务失败: {str(e)}")
            return False

if __name__ == "__main__":
    print("🚀 开始创建测试用户...")
    
    user_id = asyncio.run(create_test_user())
    
    if user_id:
        print(f"\n🎉 用户创建/获取成功！用户ID: {user_id}")
        
        # 更新任务的用户ID
        print(f"\n🚀 更新测试任务的用户ID...")
        success = asyncio.run(update_task_user())
        
        if success:
            print(f"\n🎉 测试环境准备完成！")
            print(f"👤 用户名: test_user")
            print(f"🔑 密码: test123456")
            print(f"🔗 登录链接: http://localhost:3001/login")
            print(f"📊 统计报告: http://localhost:3001/statistics-report/test_structure_display_001")
        else:
            print(f"\n💥 更新任务失败！")
    else:
        print(f"\n💥 用户创建失败！")
