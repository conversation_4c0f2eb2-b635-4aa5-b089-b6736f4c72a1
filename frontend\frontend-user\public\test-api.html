<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>检测标准API测试</h1>
    <button onclick="testApi()">测试API</button>
    <div id="result"></div>

    <script>
        async function testApi() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/system/detection-standards/hbkj_bachelor_2024');
                const data = await response.json();
                
                console.log('API响应:', data);
                
                console.log('完整API响应:', data);

                if (data.success && data.data && data.data.document_structure) {
                    const structure = data.data.document_structure;
                    const zhixie = structure.find(s => s.name === '致谢');
                    const fulu = structure.find(s => s.name === '附录');

                    resultDiv.innerHTML = `
                        <h2>✅ API调用成功</h2>
                        <p><strong>结构数量:</strong> ${structure.length}</p>
                        <p><strong>致谢:</strong> required = ${zhixie ? zhixie.required : 'not found'} ${zhixie && !zhixie.required ? '✅ 正确(可选)' : '❌'}</p>
                        <p><strong>附录:</strong> required = ${fulu ? fulu.required : 'not found'} ${fulu && !fulu.required ? '✅ 正确(可选)' : '❌'}</p>
                        <h3>完整结构:</h3>
                        <ul>
                            ${structure.map(s => `<li><strong>${s.name}:</strong> ${s.required ? '必需 🔴' : '可选 🔵'}</li>`).join('')}
                        </ul>
                        <h3>原始数据:</h3>
                        <pre style="background: #f5f5f5; padding: 10px; font-size: 12px; overflow: auto; max-height: 200px;">${JSON.stringify(data.data.document_structure, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h2>❌ API响应格式错误</h2>
                        <p>期望格式: {success: true, data: {document_structure: [...]}}</p>
                        <h3>实际响应:</h3>
                        <pre style="background: #f5f5f5; padding: 10px; font-size: 12px; overflow: auto; max-height: 300px;">${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('API调用失败:', error);
                resultDiv.innerHTML = `<p>API调用失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
