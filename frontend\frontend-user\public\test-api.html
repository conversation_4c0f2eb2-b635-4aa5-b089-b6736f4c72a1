<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>检测标准API测试</h1>
    <button onclick="testApi()">测试API</button>
    <div id="result"></div>

    <script>
        async function testApi() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/system/detection-standards/hbkj_bachelor_2024');
                const data = await response.json();
                
                console.log('API响应:', data);
                
                if (data.success && data.data && data.data.document_structure) {
                    const structure = data.data.document_structure;
                    const zhixie = structure.find(s => s.name === '致谢');
                    const fulu = structure.find(s => s.name === '附录');
                    
                    resultDiv.innerHTML = `
                        <h2>API调用成功</h2>
                        <p>结构数量: ${structure.length}</p>
                        <p>致谢: required = ${zhixie ? zhixie.required : 'not found'}</p>
                        <p>附录: required = ${fulu ? fulu.required : 'not found'}</p>
                        <h3>完整结构:</h3>
                        <ul>
                            ${structure.map(s => `<li>${s.name}: ${s.required ? '必需' : '可选'}</li>`).join('')}
                        </ul>
                    `;
                } else {
                    resultDiv.innerHTML = `<p>API响应格式错误: ${JSON.stringify(data)}</p>`;
                }
            } catch (error) {
                console.error('API调用失败:', error);
                resultDiv.innerHTML = `<p>API调用失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
