#!/usr/bin/env python3
"""
文档结构分析端到端测试

测试目标：
1. 验证基于文字居中的标题识别功能
2. 分析test.docx文档的真实结构
3. 检查outline数据的完整性和准确性
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.document_processor import DocumentProcessor
from app.core.config import settings
import structlog

# 配置日志
logger = structlog.get_logger(__name__)

def test_document_structure_analysis():
    """测试文档结构分析功能"""
    
    # 测试文档路径（从backend目录向上一级到项目根目录）
    test_doc_path = project_root.parent / "docs" / "test.docx"
    
    if not test_doc_path.exists():
        print(f"❌ 测试文档不存在: {test_doc_path}")
        return False
    
    print(f"🔍 开始测试文档结构分析")
    print(f"📄 测试文档: {test_doc_path}")
    print("=" * 60)
    
    try:
        # 初始化文档处理器
        processor = DocumentProcessor()
        
        # 1. 测试基本文档信息获取
        print("\n📋 1. 获取文档基本信息")
        print("-" * 30)
        
        doc_info = processor.get_document_info(str(test_doc_path))
        print(f"✅ 文档信息获取成功")
        print(f"   - 页数: {doc_info.get('pages', 'N/A')}")
        print(f"   - 字数: {doc_info.get('words', 'N/A')}")
        print(f"   - 段落数: {doc_info.get('paragraphs', 'N/A')}")
        
        # 2. 测试文档结构分析
        print("\n🏗️ 2. 分析文档结构")
        print("-" * 30)
        
        structure = processor.analyze_document_structure(str(test_doc_path))
        
        # 检查返回的数据结构
        print(f"✅ 文档结构分析完成")
        print(f"   - outline条目数: {len(structure.get('outline', []))}")
        print(f"   - 使用的样式数: {len(structure.get('styles_used', []))}")
        print(f"   - 节数: {len(structure.get('sections', []))}")
        
        # 3. 详细分析outline数据
        print("\n📝 3. 详细分析outline数据")
        print("-" * 30)
        
        outline = structure.get('outline', [])
        if not outline:
            print("❌ 没有找到任何outline数据")
            return False
        
        print(f"找到 {len(outline)} 个标题项目:")
        print()
        
        for i, item in enumerate(outline, 1):
            text = item.get('text', '').strip()
            style = item.get('style', 'N/A')
            level = item.get('level', 0)
            heading_type = item.get('type', 'unknown')
            alignment = item.get('alignment', 'unknown')
            paragraph_index = item.get('paragraph_index', 0)
            
            # 格式化显示
            type_icon = {
                'style': '🎨',
                'centered': '📍',
                'unknown': '❓'
            }.get(heading_type, '❓')
            
            alignment_icon = {
                'center': '⬛',
                'left': '⬅️',
                'right': '➡️',
                'justify': '↔️'
            }.get(alignment, '❓')
            
            print(f"{i:2d}. {type_icon} [{heading_type}] {alignment_icon} H{level} | {text}")
            print(f"     样式: {style} | 段落: {paragraph_index}")
            print()
        
        # 4. 分析居中标题识别效果
        print("\n📍 4. 居中标题识别统计")
        print("-" * 30)
        
        style_count = sum(1 for item in outline if item.get('type') == 'style')
        centered_count = sum(1 for item in outline if item.get('type') == 'centered')
        center_aligned_count = sum(1 for item in outline if item.get('alignment') == 'center')
        
        print(f"   - Word样式标题: {style_count} 个")
        print(f"   - 居中识别标题: {centered_count} 个")
        print(f"   - 居中对齐标题: {center_aligned_count} 个")
        
        # 5. 检查特定的预期标题
        print("\n🎯 5. 检查预期的标题内容")
        print("-" * 30)
        
        expected_titles = [
            "学位论文原创性声明",
            "学位论文版权使用授权书"
        ]
        
        found_titles = [item.get('text', '').strip() for item in outline]
        
        for expected in expected_titles:
            found = any(expected in title for title in found_titles)
            status = "✅ 找到" if found else "❌ 未找到"
            print(f"   {status}: {expected}")
        
        # 6. 输出完整的JSON数据用于调试
        print("\n📊 6. 完整结构数据")
        print("-" * 30)
        
        output_file = project_root / "test_structure_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 完整数据已保存到: {output_file}")
        
        # 7. 测试结果总结
        print("\n📈 7. 测试结果总结")
        print("-" * 30)
        
        success_criteria = [
            (len(outline) > 0, "找到标题项目"),
            (centered_count > 0, "识别到居中标题"),
            (any("声明" in item.get('text', '') for item in outline), "找到声明相关标题"),
            (any("授权" in item.get('text', '') for item in outline), "找到授权相关标题")
        ]
        
        passed = sum(1 for criteria, _ in success_criteria if criteria)
        total = len(success_criteria)
        
        print(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        print()
        
        for criteria, description in success_criteria:
            status = "✅" if criteria else "❌"
            print(f"   {status} {description}")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 文档结构分析端到端测试")
    print("=" * 60)
    
    success = test_document_structure_analysis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成 - 所有检查项通过")
        sys.exit(0)
    else:
        print("❌ 测试失败 - 部分检查项未通过")
        sys.exit(1)

if __name__ == "__main__":
    main()
