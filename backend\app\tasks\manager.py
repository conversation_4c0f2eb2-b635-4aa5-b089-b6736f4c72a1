"""
Word文档分析服务 - 任务管理器
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

from app.core.logging import logger
from app.database import crud
from app.database.session import session_manager
from app.models import TaskUpdate, TaskStatus, TaskType
from app.core.exceptions import TaskException
from app.tasks.queue import get_task_queue, TaskPriority
from app.tasks.worker import get_worker_pool
from app.tasks.progress import get_progress_tracker
# 移除无用的concurrency导入


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.is_initialized = False
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.max_concurrent_tasks = 5
        self.worker_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        self.queue_lock = asyncio.Lock()  # Lock for safe queue manipulation
    
    async def initialize(self):
        """初始化任务管理器"""
        logger.info("正在初始化任务管理器...")
        
        # 启动工作线程
        for i in range(self.max_concurrent_tasks):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(worker)
        
        # 恢复未完成的任务
        await self._recover_pending_tasks()
        
        self.is_initialized = True
        logger.info(f"任务管理器初始化完成，启动了 {len(self.worker_tasks)} 个工作线程")
    
    async def shutdown(self):
        """关闭任务管理器"""
        logger.info("正在关闭任务管理器...")
        
        # 设置关闭标志
        self._shutdown_event.set()
        
        # 取消所有运行中的任务
        for task_id, task in self.running_tasks.items():
            if not task.done():
                task.cancel()
                logger.info(f"取消运行中的任务: {task_id}")
        
        # 等待所有工作线程完成
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        self.is_initialized = False
        logger.info("任务管理器已关闭")
    
    async def create_task(self, task_type: str, file_path: str, **kwargs):
        """创建新任务"""
        try:
            from app.models.task import TaskCreate, TaskType
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 创建任务数据
            task_data = TaskCreate(
                task_id=task_id,
                task_type=TaskType(task_type),
                file_path=file_path,
                status=TaskStatus.PENDING,
                created_at=datetime.now(),
                **kwargs
            )
            
            # 保存到数据库
            task = await session_manager.execute_crud_operation(crud.create_task, task_data)
            
            # 添加到处理队列
            await self.process_task(task_id)
            
            logger.info(f"任务创建成功: {task_id}")
            return task
            
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            raise TaskException(f"创建任务失败: {str(e)}")

    async def process_task(self, task_id: str):
        """处理任务（添加到队列）"""
        try:
            # 验证任务是否存在
            task = await session_manager.execute_crud_operation(crud.get_task, task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            # 添加到队列
            await self.task_queue.put(task_id)
            logger.info(f"任务已添加到队列: {task_id}")
            
        except Exception as e:
            logger.error(f"添加任务到队列失败: {task_id} - {str(e)}")
    
    async def cancel_task(self, task_id: str):
        """
        取消任务

        - 如果任务正在运行，则取消其asyncio.Task。
        - 如果任务处于PENDING状态，则从队列中安全地移除。
        - 最后将数据库中的状态更新为CANCELLED。
        """
        try:
            # 检查任务是否在运行中
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                if not task.done():
                    task.cancel()
                    logger.info(f"已取消运行中的任务: {task_id}")
            
            # 检查数据库中的任务状态
            task_in_db = await session_manager.execute_crud_operation(crud.get_task, task_id)
            if not task_in_db:
                logger.warning(f"尝试取消一个不存在于数据库的任务: {task_id}")
                return

            # 如果任务是PENDING，则尝试从队列中移除
            if task_in_db.status == TaskStatus.PENDING:
                async with self.queue_lock:
                    # 从队列中安全地移除任务
                    items_in_queue = []
                    while not self.task_queue.empty():
                        items_in_queue.append(self.task_queue.get_nowait())

                    task_removed = False
                    new_queue_items = []
                    for item in items_in_queue:
                        if item == task_id:
                            task_removed = True
                        else:
                            new_queue_items.append(item)
                    
                    for item in new_queue_items:
                        await self.task_queue.put(item)

                    if task_removed:
                        logger.info(f"已从队列中移除 PENDING 任务: {task_id}")
                    else:
                        logger.warning(f"任务 {task_id} 状态为 PENDING 但不在队列中，可能已被领取")

            # 统一更新数据库状态
            task_update = TaskUpdate(
                status=TaskStatus.CANCELLED,
                completed_at=datetime.now(),
                updated_at=datetime.now(),
                error_message="任务被用户手动取消"
            )
            await session_manager.execute_crud_operation(crud.update_task, task_id, task_update)
            logger.info(f"任务 {task_id} 状态已更新为 CANCELLED")

        except Exception as e:
            logger.error(f"取消任务失败: {task_id} - {str(e)}")
            raise TaskException(f"取消任务时发生严重错误: {task_id}") from e
    
    async def get_queue_status(self):
        """获取队列状态"""
        return {
            "queue_size": self.task_queue.qsize(),
            "running_tasks": len(self.running_tasks),
            "worker_count": len(self.worker_tasks),
            "max_concurrent": self.max_concurrent_tasks
        }
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"工作线程启动: {worker_name}")
        
        while not self._shutdown_event.is_set():
            try:
                # 从队列获取任务（带超时）
                task_id = await asyncio.wait_for(
                    self.task_queue.get(),
                    timeout=1.0
                )
                
                # 处理任务
                await self._execute_task(task_id, worker_name)
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except asyncio.CancelledError:
                # 工作线程被取消
                break
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 发生错误: {str(e)}")
        
        logger.info(f"工作线程停止: {worker_name}")
    
    async def _execute_task(self, task_id: str, worker_name: str):
        """执行具体任务"""
        try:
            # 获取任务信息
            task = await session_manager.execute_crud_operation(crud.get_task, task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            # 检查任务状态
            if task.status != TaskStatus.PENDING:
                logger.warning(f"任务状态不是 pending: {task_id} - {task.status}")
                return
            
            logger.info(f"开始执行任务: {task_id} (工作线程: {worker_name})")
            
            # 更新任务状态为处理中
            await session_manager.execute_crud_operation(crud.update_task, task_id, TaskUpdate(
                status=TaskStatus.PROCESSING,
                updated_at=datetime.now()
            ))
            
            # 创建任务协程
            task_coroutine = self._process_task_by_type(task)
            task_future = asyncio.create_task(task_coroutine)
            
            # 记录运行中的任务
            self.running_tasks[task_id] = task_future
            
            try:
                # 执行任务
                result = await task_future

                # 注意：任务状态的更新现在由progress_tracker.complete_task()负责
                # 这样可以确保进度跟踪和数据库状态的一致性

                # 创建文档记录
                await self._create_document_record(task, result)
                
                logger.info(f"任务执行成功: {task_id}")
                
            except asyncio.CancelledError:
                # 任务被取消
                await session_manager.execute_crud_operation(crud.update_task, task_id, TaskUpdate(
                    status=TaskStatus.CANCELLED,
                    completed_at=datetime.now(),
                    updated_at=datetime.now()
                ))
                logger.info(f"任务被取消: {task_id}")
                
            except Exception as e:
                # 任务执行失败
                # 注意：任务失败状态的更新现在由progress_tracker.fail_task()负责
                # 这样可以确保进度跟踪和数据库状态的一致性
                logger.error(f"任务执行失败: {task_id} - {str(e)}")

                # 确保进度跟踪器也知道任务失败了
                progress_tracker = get_progress_tracker()
                await progress_tracker.fail_task(task_id, str(e))
            
            finally:
                # 从运行中任务列表移除
                self.running_tasks.pop(task_id, None)
                
        except Exception as e:
            logger.error(f"执行任务时发生错误: {task_id} - {str(e)}")
    
    async def _create_document_record(self, task, result):
        """创建文档记录"""
        try:
            from app.models.document import DocumentCreate
            
            # 从任务结果中提取文档信息
            analysis_result = result.get('analysis_result', {}) if isinstance(result, dict) else {}
            content_stats = analysis_result.get('content_stats', {}) if isinstance(analysis_result, dict) else {}
            
            # 创建文档记录
            document_data = DocumentCreate(
                document_id=task.task_id,  # 使用任务ID作为文档ID
                task_id=task.task_id,
                filename=task.filename,
                file_size=task.file_size,
                created_date=task.created_at,
                # 从分析结果中提取统计信息
                page_count=content_stats.get('page_count', 0),
                word_count=content_stats.get('word_count', 0),
                paragraph_count=content_stats.get('paragraph_count', 0),
                image_count=content_stats.get('image_count', 0),
                table_count=content_stats.get('table_count', 0)
            )
            
            # 保存文档记录到数据库
            document = await session_manager.execute_crud_operation(crud.create_document, document_data)
            logger.info(f"文档记录创建成功: {task.task_id} - {task.filename}")
            
        except Exception as e:
            logger.error(f"创建文档记录失败: {task.task_id} - {str(e)}")
            # 不要抛出异常，因为任务已经成功完成，只是文档记录创建失败
    
    async def _process_task_by_type(self, task):
        """根据任务类型处理任务"""
        try:
            if task.task_type == TaskType.PAPER_CHECK:
                return await self._process_paper_check_task(task)
            elif task.task_type == TaskType.CONTENT_ANALYSIS:
                return await self._process_content_analysis_task(task)
            elif task.task_type == TaskType.FORMAT_CHECK:
                return await self._process_format_check_task(task)
            else:
                raise TaskException(f"不支持的任务类型: {task.task_type}")
                
        except Exception as e:
            logger.error(f"处理任务失败: {task.task_id} - {str(e)}")
            raise
    
    async def _process_paper_check_task(self, task):
        """处理论文检测任务"""
        logger.info(f"开始论文检测: {task.task_id}")
        
        # 获取进度跟踪器
        progress_tracker = await get_progress_tracker()
        
        # 定义任务步骤
        steps = [
            "文档上传验证",
            "文档内容提取",
            "格式规则检测",
            "生成分析报告"
        ]
        
        # 初始化进度跟踪
        await progress_tracker.start_task(
            task.task_id, 
            "paper_check", 
            steps
        )
        
        # 实现实际的论文检测逻辑
        try:
            # 🔥 新增：解析检测标准选项
            detection_standard = None
            if hasattr(task, 'analysis_options') and task.analysis_options:
                detection_standard = task.analysis_options.get('detection_standard')
                logger.info(f"检测标准: {detection_standard}")
            
            # ========== 步骤1: 文档上传验证 ==========
            await progress_tracker.update_progress(task.task_id, 10, "step_1", "验证文档格式和完整性")
            
            # 验证文件是否存在和可读
            from pathlib import Path
            if not Path(task.file_path).exists():
                raise TaskException(f"文档文件不存在: {task.file_path}")
            
            await asyncio.sleep(0.5)  # 模拟文件验证时间
            await progress_tracker.complete_step(task.task_id, "step_1", "文档验证完成")
            
            # ========== 步骤2: 文档内容提取 ==========
            await progress_tracker.update_progress(task.task_id, 25, "step_2", "正在提取文档内容和结构")
            
            from app.services.document_analyzer import DocumentAnalyzer
            analyzer = DocumentAnalyzer()
            
            # 实际的文档分析（这是最耗时的步骤）
            analysis_result = await analyzer.analyze_document(task.file_path)

            # 🔥 关键修复：检查分析是否成功
            if not analysis_result.success:
                await progress_tracker.fail_task(task.task_id, f"文档分析失败: {analysis_result.error_message}", "step_2")
                raise TaskException(f"文档分析核心模块失败: {analysis_result.error_message}")
            
            await progress_tracker.update_progress(task.task_id, 50, "step_2", "文档内容提取完成")
            await progress_tracker.complete_step(task.task_id, "step_2", "内容提取成功")
            
            # ========== 步骤3: 格式规则检测 ==========
            await progress_tracker.update_progress(task.task_id, 60, "step_3", "正在执行格式规则检测")
            
            # 根据检测标准选择规则文件
            rule_file_path = "config/rules/hbkj_bachelor_2024.json"  # 默认规则文件
            if detection_standard:
                # 如果指定了检测标准，使用特定的规则文件
                if detection_standard == 'hbkj_bachelor_2024':
                    rule_file_path = "config/rules/hbkj_bachelor_2024.json"
                elif detection_standard == 'gbt_7713_1_2006':
                    # GB/T 7713.1-2006: 学位论文编写规则
                    rule_file_path = "config/rules/gbt_7713_1_2006.json"
                elif detection_standard == 'gbt_7714_2015':
                    # GB/T 7714-2015: 参考文献著录规则
                    rule_file_path = "config/rules/gbt_7714_2015.json"
                else:
                    logger.warning(f"未知的检测标准: {detection_standard}，使用默认规则")

            await progress_tracker.update_progress(task.task_id, 70, "step_3", "加载检测规则")

            # 创建规则引擎并加载规则
            from app.checkers.rule_engine import RuleEngine
            from app.services.document_analyzer import CHECK_FUNCTIONS
            rule_engine = RuleEngine(CHECK_FUNCTIONS)

            # 加载规则文件
            rule_engine.load_rules_from_file(rule_file_path)

            await progress_tracker.update_progress(task.task_id, 80, "step_3", "执行格式检测规则")

            # 创建DocumentData对象传递给规则引擎
            from app.services.document_processor import DocumentData
            document_data = DocumentData(
                file_path=task.file_path,
                doc_info=getattr(analysis_result, 'document_info', {}),
                content_stats=getattr(analysis_result, 'content_analysis', {}),
                elements=[],  # 这些字段需要从analysis_result中提取，暂时为空
                paragraphs=[],
                tables=[],
                images=[]
            )

            # 执行论文格式检查
            check_result = await rule_engine.execute_check(document_data)
            
            await progress_tracker.complete_step(task.task_id, "step_3", "格式检测完成")
            
            # ========== 步骤4: 生成分析报告 ==========
            await progress_tracker.update_progress(task.task_id, 90, "step_4", "正在生成分析报告")
            
            # 转换分析结果为前端期望的格式
            converted_analysis = self._convert_analysis_result_to_dict(analysis_result)
            
            # 🔥 关键修复：将封面页信息添加到document_info中
            cover_page_info = self._extract_cover_page_info_from_analysis(analysis_result)
            if 'document_info' not in converted_analysis:
                converted_analysis['document_info'] = {}
            converted_analysis['document_info']['cover_page_info'] = cover_page_info
            
            await asyncio.sleep(0.3)  # 模拟报告生成时间
            
            await progress_tracker.update_progress(task.task_id, 100, "step_4", "报告生成完成")
            await progress_tracker.complete_step(task.task_id, "step_4", "分析报告已生成")
            
            # 🔥 修复：根据detection_standard从config.py获取正确的标准名称
            from app.core.config import DETECTION_STANDARD_NAMES
            standard_name = DETECTION_STANDARD_NAMES.get(detection_standard, '论文检测')

            # 完成任务
            result = {
                "task_type": "paper_check",
                "status": "completed",
                "compliance_score": check_result.get("compliance_score", 85.0) if isinstance(check_result, dict) else 85.0,
                "problems_found": len(check_result.get("problems", [])) if isinstance(check_result, dict) else 0,
                "processing_time": 2.0,
                "analysis_result": converted_analysis,
                "check_result": check_result,
                "detection_standard": detection_standard,  # 新增：返回使用的检测标准
                "standard_name": standard_name,  # 🔥 修复：使用config.py中定义的正确标准名称
                # 🔥 修复：封面页信息现在已经包含在converted_analysis的document_info中
                "document_info": converted_analysis.get('document_info', {})
            }
            
            # 🔥 调试：添加详细的日志记录
            logger.info(f"准备完成任务 {task.task_id}")
            logger.info(f"结果类型: {type(result)}")
            logger.info(f"结果大小: {len(str(result))} 字符")
            logger.info(f"document_info存在: {'document_info' in result}")
            logger.info(f"document_info内容: {result.get('document_info', {})}")

            # 检查封面页信息
            cover_info = result.get('document_info', {}).get('cover_page_info', {})
            if cover_info:
                logger.info(f"封面页信息已提取: {list(cover_info.keys())}")
                for key, value in cover_info.items():
                    if key != 'raw_text':  # 跳过原始文本，太长了
                        logger.info(f"  {key}: {value}")
            else:
                logger.warning("封面页信息为空或不存在")

            await progress_tracker.complete_task(task.task_id, "论文检测任务完成", result)
            
        except Exception as e:
            logger.error(f"论文检测执行失败: {str(e)}")
            
            # 标记任务失败
            await progress_tracker.fail_task(task.task_id, str(e))
            
            result = {
                "task_type": "paper_check",
                "status": "failed",
                "error": str(e),
                "processing_time": 2.0,
                "detection_standard": detection_standard,
                "analysis_result": {
                    "content_stats": {
                        "page_count": 0,
                        "word_count": 0,
                        "paragraph_count": 0,
                        "image_count": 0,
                        "table_count": 0
                    }
                }
            }
        
        logger.info(f"论文检测完成: {task.task_id}")
        return result
    
    async def _process_content_analysis_task(self, task):
        """处理内容分析任务"""
        logger.info(f"开始内容分析: {task.task_id}")
        
        # 模拟处理过程
        await asyncio.sleep(1.5)
        
        # 实现实际的内容分析逻辑
        try:
            from app.services.document_analyzer import DocumentAnalyzer
            from app.services.image_processor import get_image_processor
            
            # 1. 解析文档结构和内容
            analyzer = DocumentAnalyzer()
            analysis_result = await analyzer.analyze_document(task.file_path)

            # 🔥 关键修复：检查分析是否成功
            if not analysis_result.success:
                raise TaskException(f"文档分析核心模块失败: {analysis_result.error_message}")
            
            # 2. 提取图片信息
            image_processor = get_image_processor()
            image_result = image_processor.extract_images_from_document(task.file_path, task.task_id)
            
            # 3. 转换分析结果并生成内容摘要
            converted_analysis = self._convert_analysis_result_to_dict(analysis_result)
            content_stats = converted_analysis.get("analysis_result", {}).get("content_stats", {})
            
            result = {
                "task_type": "content_analysis",
                "status": "completed",
                "word_count": content_stats.get("word_count", 0),
                "paragraph_count": content_stats.get("paragraph_count", 0),
                "image_count": len(image_result.get("images", [])) if isinstance(image_result, dict) else 0,
                "table_count": content_stats.get("table_count", 0),
                "processing_time": 1.5,
                "analysis_result": converted_analysis,
                "image_result": image_result
            }
            
        except Exception as e:
            logger.error(f"内容分析执行失败: {str(e)}")
            result = {
                "task_type": "content_analysis",
                "status": "failed",
                "error": str(e),
                "processing_time": 1.5,
                "analysis_result": {
                    "content_stats": {
                        "page_count": 0,
                        "word_count": 0,
                        "paragraph_count": 0,
                        "image_count": 0,
                        "table_count": 0
                    }
                }
            }
        
        logger.info(f"内容分析完成: {task.task_id}")
        return result
    
    async def _process_format_check_task(self, task):
        """处理格式检查任务"""
        logger.info(f"开始格式检查: {task.task_id}")
        
        # 模拟处理过程
        await asyncio.sleep(1.0)
        
        # 实现实际的格式检查逻辑
        try:
            from app.services.document_analyzer import DocumentAnalyzer
            from app.checkers.format_checker import get_format_checker
            
            # 1. 解析文档格式信息
            analyzer = DocumentAnalyzer()
            analysis_result = await analyzer.analyze_document(task.file_path)

            # 🔥 关键修复：检查分析是否成功
            if not analysis_result.success:
                raise TaskException(f"文档分析核心模块失败: {analysis_result.error_message}")
            
            # 2. 执行格式检查
            format_checker = get_format_checker()
            format_result = await format_checker.check_document_format(analysis_result)
            
            # 3. 转换分析结果为前端期望的格式
            converted_analysis = self._convert_analysis_result_to_dict(analysis_result)
            
            result = {
                "task_type": "format_check",
                "status": "completed",
                "format_score": format_result.get("format_score", 90.0) if isinstance(format_result, dict) else 90.0,
                "format_issues": len(format_result.get("issues", [])) if isinstance(format_result, dict) else 0,
                "processing_time": 1.0,
                "analysis_result": converted_analysis,
                "format_result": format_result
            }
            
        except Exception as e:
            logger.error(f"格式检查执行失败: {str(e)}")
            result = {
                "task_type": "format_check",
                "status": "failed", 
                "error": str(e),
                "processing_time": 1.0,
                "analysis_result": {
                    "content_stats": {
                        "page_count": 0,
                        "word_count": 0,
                        "paragraph_count": 0,
                        "image_count": 0,
                        "table_count": 0
                    }
                }
            }
        
        logger.info(f"格式检查完成: {task.task_id}")
        return result
    
    async def _recover_pending_tasks(self):
        """恢复未完成的任务"""
        try:
            # 获取所有待处理的任务
            pending_tasks = await session_manager.execute_crud_operation(crud.get_tasks, 0, 100, TaskStatus.PENDING)
            
            if pending_tasks:
                logger.info(f"发现 {len(pending_tasks)} 个待处理任务，正在恢复...")
                
                for task in pending_tasks:
                    await self.process_task(task.task_id)
                    
                logger.info(f"已恢复 {len(pending_tasks)} 个待处理任务")
            else:
                logger.info("没有待处理的任务需要恢复")
                
        except Exception as e:
            logger.error(f"恢复待处理任务失败: {str(e)}")

    def _convert_analysis_result_to_dict(self, analysis_result) -> Dict[str, Any]:
        """
        将AnalysisResult dataclass转换为前端期望的字典格式
        
        Args:
            analysis_result: AnalysisResult对象或数据库中存储的字典
            
        Returns:
            Dict[str, Any]: 转换后的字典格式，包含前端需要的路径结构
        """
        if analysis_result is None:
            return {}
        
        # 🔥 关键修复：处理数据库中存储的字典格式
        if isinstance(analysis_result, dict):
            # 检查是否已经是前端期望的格式
            if 'analysis_result' in analysis_result and 'content_stats' in analysis_result.get('analysis_result', {}):
                logger.info("数据已经是前端期望的格式，直接返回")
                return analysis_result
            
            # 如果不是，尝试从字典中构建前端期望的格式
            return self._convert_dict_to_frontend_format(analysis_result)
        
        # 如果是AnalysisResult dataclass
        if hasattr(analysis_result, 'success'):
            result_dict = {
                "success": analysis_result.success,
                "processing_time": getattr(analysis_result, 'processing_time', 0),
                "error_message": getattr(analysis_result, 'error_message', None),
                "warnings": getattr(analysis_result, 'warnings', [])
            }
            
            # 添加document_info（确保可序列化）
            if hasattr(analysis_result, 'document_info') and analysis_result.document_info:
                result_dict["document_info"] = self._serialize_document_info(analysis_result.document_info)
            
            # 处理content_analysis，提取统计信息和结构数据
            content_stats = {}
            document_structures = []
            outline = []
            structure_analysis = {}

            if hasattr(analysis_result, 'content_analysis') and analysis_result.content_analysis:
                content_analysis = analysis_result.content_analysis

                # 尝试提取统计信息的多种方式
                if hasattr(content_analysis, 'content_stats'):
                    content_stats = content_analysis.content_stats
                elif hasattr(content_analysis, 'to_dict'):
                    content_data = content_analysis.to_dict()
                    content_stats = content_data.get("content_stats", {})
                elif isinstance(content_analysis, dict):
                    content_stats = content_analysis.get("content_stats", {})
                else:
                    # 尝试从对象属性提取
                    for attr in ['page_count', 'word_count', 'paragraph_count', 'image_count', 'table_count', 'formula_count', 'reference_count', 'footnote_count']:
                        if hasattr(content_analysis, attr):
                            content_stats[attr] = getattr(content_analysis, attr)

                # 🔥 新增：提取文档结构数据
                if hasattr(content_analysis, 'document_structures'):
                    document_structures = content_analysis.document_structures or []
                if hasattr(content_analysis, 'outline'):
                    outline = content_analysis.outline or []
                if hasattr(content_analysis, 'structure_analysis'):
                    structure_analysis = content_analysis.structure_analysis or {}

                logger.info(f"提取到结构数据: {len(document_structures)} 个结构, {len(outline)} 个大纲条目")

                # 不直接存储content_analysis对象，避免序列化问题
                # result_dict["content_analysis"] = content_analysis
            
            # 🔥 关键修复：如果content_analysis没有提供数据，从formatted_json中提取
            if not content_stats and hasattr(analysis_result, 'formatted_json') and analysis_result.formatted_json:
                try:
                    formatted_data = json.loads(analysis_result.formatted_json)
                    if 'content_analysis' in formatted_data:
                        json_content_stats = formatted_data['content_analysis']
                        content_stats = {
                            'page_count': json_content_stats.get('page_count', 0),
                            'word_count': json_content_stats.get('word_count', 0),
                            'paragraph_count': json_content_stats.get('paragraph_count', 0),
                            'image_count': json_content_stats.get('image_count', 0),
                            'table_count': json_content_stats.get('table_count', 0),
                            'formula_count': json_content_stats.get('formula_count', 0),
                            'reference_count': json_content_stats.get('reference_count', 0),
                            'footnote_count': json_content_stats.get('footnote_count', 0)
                        }
                except Exception as e:
                    logger.warning(f"从formatted_json提取content_stats失败: {e}")
            
            # 确保content_stats格式正确
            if not isinstance(content_stats, dict):
                content_stats = {}
            
            # 设置默认值
            default_stats = {
                "page_count": 0,
                "word_count": 0,
                "paragraph_count": 0,
                "image_count": 0,
                "table_count": 0,
                "formula_count": 0,
                "reference_count": 0,
                "footnote_count": 0
            }
            
            # 合并统计数据
            for key, default_value in default_stats.items():
                if key not in content_stats:
                    content_stats[key] = default_value
            
            # 创建前端期望的analysis_result结构
            result_dict["analysis_result"] = {
                "content_stats": content_stats,
                "structure_analysis": structure_analysis
            }

            # 🔥 新增：在顶级添加文档结构数据（前端期望的格式）
            result_dict["document_structures"] = document_structures
            result_dict["outline"] = outline
            
            # 添加其他分析结果（确保可序列化）
            if hasattr(analysis_result, 'format_analysis') and analysis_result.format_analysis:
                try:
                    if hasattr(analysis_result.format_analysis, 'to_dict'):
                        result_dict["analysis_result"]["format_analysis"] = analysis_result.format_analysis.to_dict()
                    elif isinstance(analysis_result.format_analysis, dict):
                        result_dict["analysis_result"]["format_analysis"] = analysis_result.format_analysis
                    else:
                        result_dict["analysis_result"]["format_analysis"] = str(analysis_result.format_analysis)
                except Exception:
                    pass
            
            if hasattr(analysis_result, 'structure_analysis') and analysis_result.structure_analysis:
                try:
                    if hasattr(analysis_result.structure_analysis, 'to_dict'):
                        result_dict["analysis_result"]["structure_analysis"] = analysis_result.structure_analysis.to_dict()
                    elif isinstance(analysis_result.structure_analysis, dict):
                        result_dict["analysis_result"]["structure_analysis"] = analysis_result.structure_analysis
                    else:
                        result_dict["analysis_result"]["structure_analysis"] = str(analysis_result.structure_analysis)
                except Exception:
                    pass
            
            if hasattr(analysis_result, 'formatted_json') and analysis_result.formatted_json:
                result_dict["formatted_json"] = analysis_result.formatted_json
            
            return result_dict
        
        # 其他类型，尝试转换为字典
        try:
            if hasattr(analysis_result, '__dict__'):
                return analysis_result.__dict__
            else:
                return {"raw_result": str(analysis_result)}
        except Exception as e:
            logger.warning(f"无法转换分析结果: {e}")
            return {"error": f"转换失败: {str(e)}"}

    def _convert_dict_to_frontend_format(self, result_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        将数据库中存储的字典格式转换为前端期望的格式
        
        Args:
            result_dict: 数据库中存储的结果字典
            
        Returns:
            Dict[str, Any]: 前端期望的格式
        """
        logger.info("开始转换字典格式到前端期望格式")
        
        # 设置默认的统计数据
        default_stats = {
            "page_count": 0,
            "word_count": 0,
            "paragraph_count": 0,
            "image_count": 0,
            "table_count": 0,
            "formula_count": 0,
            "reference_count": 0,
            "footnote_count": 0
        }
        
        content_stats = {}
        
        # 1. 尝试从顶层document_info中提取统计信息
        if 'document_info' in result_dict and isinstance(result_dict['document_info'], dict):
            doc_info = result_dict['document_info']
            content_stats.update({
                'page_count': doc_info.get('pages', 0),
                'word_count': doc_info.get('words', 0),
                'paragraph_count': doc_info.get('paragraphs', 0),
                'image_count': doc_info.get('images', 0),
                'table_count': doc_info.get('tables', 0),
            })
        
        # 🔥 关键修复：从嵌套的analysis_result.document_info中提取统计信息
        if 'analysis_result' in result_dict and isinstance(result_dict['analysis_result'], dict):
            nested_analysis = result_dict['analysis_result']
            if 'document_info' in nested_analysis and isinstance(nested_analysis['document_info'], dict):
                nested_doc_info = nested_analysis['document_info']
                logger.info(f"发现嵌套document_info: {nested_doc_info}")
                
                # 优先使用嵌套的document_info数据
                content_stats.update({
                    'page_count': nested_doc_info.get('pages', content_stats.get('page_count', 0)),
                    'word_count': nested_doc_info.get('words', content_stats.get('word_count', 0)),
                    'paragraph_count': nested_doc_info.get('paragraphs', content_stats.get('paragraph_count', 0)),
                    'image_count': nested_doc_info.get('images', content_stats.get('image_count', 0)),
                    'table_count': nested_doc_info.get('tables', content_stats.get('table_count', 0)),
                })
            
            # 🔥 新增修复：处理双重嵌套的analysis_result.analysis_result.content_stats
            if 'analysis_result' in nested_analysis and isinstance(nested_analysis['analysis_result'], dict):
                double_nested_analysis = nested_analysis['analysis_result']
                if 'content_stats' in double_nested_analysis and isinstance(double_nested_analysis['content_stats'], dict):
                    double_nested_content_stats = double_nested_analysis['content_stats']
                    logger.info(f"发现双重嵌套content_stats: {double_nested_content_stats}")
                    
                    # 使用双重嵌套的content_stats数据（这是最准确的）
                    content_stats.update({
                        'page_count': double_nested_content_stats.get('page_count', content_stats.get('page_count', 0)),
                        'word_count': double_nested_content_stats.get('word_count', content_stats.get('word_count', 0)),
                        'paragraph_count': double_nested_content_stats.get('paragraph_count', content_stats.get('paragraph_count', 0)),
                        'image_count': double_nested_content_stats.get('image_count', content_stats.get('image_count', 0)),
                        'table_count': double_nested_content_stats.get('table_count', content_stats.get('table_count', 0)),
                        'formula_count': double_nested_content_stats.get('formula_count', content_stats.get('formula_count', 0)),
                        'reference_count': double_nested_content_stats.get('reference_count', content_stats.get('reference_count', 0)),
                        'footnote_count': double_nested_content_stats.get('footnote_count', content_stats.get('footnote_count', 0)),
                    })
        
        # 2. 尝试从formatted_json中提取统计信息
        if 'formatted_json' in result_dict and result_dict['formatted_json']:
            try:
                formatted_data = json.loads(result_dict['formatted_json'])
                if 'content_analysis' in formatted_data:
                    json_content_stats = formatted_data['content_analysis']
                    for key in ['page_count', 'word_count', 'paragraph_count', 'image_count', 'table_count', 'formula_count', 'reference_count', 'footnote_count']:
                        if key in json_content_stats:
                            content_stats[key] = json_content_stats[key]
            except Exception as e:
                logger.warning(f"从formatted_json解析统计信息失败: {e}")
        
        # 🔥 关键修复：从嵌套的analysis_result.formatted_json中提取统计信息
        if 'analysis_result' in result_dict and isinstance(result_dict['analysis_result'], dict):
            nested_analysis = result_dict['analysis_result']
            if 'formatted_json' in nested_analysis and nested_analysis['formatted_json']:
                try:
                    formatted_data = json.loads(nested_analysis['formatted_json'])
                    if 'content_analysis' in formatted_data:
                        json_content_stats = formatted_data['content_analysis']
                        logger.info(f"发现嵌套formatted_json中的content_analysis: {json_content_stats}")
                        for key in ['page_count', 'word_count', 'paragraph_count', 'image_count', 'table_count', 'formula_count', 'reference_count', 'footnote_count']:
                            if key in json_content_stats:
                                content_stats[key] = json_content_stats[key]
                except Exception as e:
                    logger.warning(f"从嵌套formatted_json解析统计信息失败: {e}")
        
        # 3. 尝试从其他字段中提取统计信息
        possible_fields = ['analysis_result', 'content_analysis', 'result', 'stats', 'statistics']
        for field in possible_fields:
            if field in result_dict and isinstance(result_dict[field], dict):
                field_data = result_dict[field]
                for key in default_stats.keys():
                    if key in field_data:
                        content_stats[key] = field_data[key]
        
        # 4. 确保所有统计字段都有值
        for key, default_value in default_stats.items():
            if key not in content_stats:
                content_stats[key] = default_value
        
        # 5. 构建前端期望的格式
        converted_result = {
            "success": result_dict.get('success', True),
            "processing_time": result_dict.get('processing_time', 0),
            "error_message": result_dict.get('error_message', None),
            "warnings": result_dict.get('warnings', []),
            "document_info": result_dict.get('document_info', {}),
            "formatted_json": result_dict.get('formatted_json', None),
            "analysis_result": {
                "content_stats": content_stats,
                # 🔥 新增：包含结构分析数据
                "structure_analysis": result_dict.get('analysis_result', {}).get('structure_analysis', {})
            },
            # 🔥 新增：包含文档结构数据（前端期望的顶级字段）
            "document_structures": result_dict.get('document_structures', []),
            "outline": result_dict.get('outline', []),
            # 🔥 修复：保留检测标准相关字段
            "detection_standard": result_dict.get('detection_standard'),
            "standard_name": result_dict.get('standard_name'),
            # 🔥 修复：保留其他任务相关字段
            "task_type": result_dict.get('task_type'),
            "status": result_dict.get('status'),
            "compliance_score": result_dict.get('compliance_score'),
            "problems_found": result_dict.get('problems_found'),
            "check_result": result_dict.get('check_result')
        }
        
        logger.info(f"转换完成，content_stats: {content_stats}")

        # 🔥 新增：调试日志 - 检查结构数据
        document_structures = converted_result.get('document_structures', [])
        outline = converted_result.get('outline', [])
        logger.info(f"转换后的结构数据: document_structures={len(document_structures)}个, outline={len(outline)}个")

        return converted_result

    def _serialize_document_info(self, document_info) -> Dict[str, Any]:
        """
        序列化document_info，处理datetime对象
        
        Args:
            document_info: 包含可能不可序列化对象的文档信息
            
        Returns:
            Dict[str, Any]: 可序列化的字典
        """
        if not document_info:
            return {}
        
        serialized = {}
        
        for key, value in document_info.items():
            try:
                if value is None:
                    serialized[key] = None
                elif isinstance(value, (str, int, float, bool)):
                    serialized[key] = value
                elif hasattr(value, 'isoformat'):  # datetime对象
                    serialized[key] = value.isoformat()
                elif isinstance(value, dict):
                    serialized[key] = self._serialize_document_info(value)
                elif isinstance(value, list):
                    serialized[key] = [self._serialize_value(item) for item in value]
                else:
                    # 其他类型转换为字符串
                    serialized[key] = str(value)
            except Exception as e:
                logger.warning(f"序列化document_info字段 {key} 失败: {e}")
                serialized[key] = str(value)
        
        return serialized
    
    def _serialize_value(self, value):
        """序列化单个值"""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif hasattr(value, 'isoformat'):  # datetime对象
            return value.isoformat()
        elif isinstance(value, dict):
            return self._serialize_document_info(value)
        elif isinstance(value, list):
            return [self._serialize_value(item) for item in value]
        else:
            return str(value)
    
    def _extract_cover_page_info_from_analysis(self, analysis_result) -> Dict[str, Any]:
        """
        从分析结果中提取封面页信息
        
        Args:
            analysis_result: 文档分析结果
            
        Returns:
            Dict[str, Any]: 封面页信息
        """
        try:
            # 默认封面页信息
            default_cover_info = {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': ''
            }
            
            # 如果分析结果为空，返回默认值
            if not analysis_result or not analysis_result.success:
                logger.warning("分析结果为空或失败，返回默认封面页信息")
                return default_cover_info
            
            # 尝试从document_info中获取封面页信息
            if hasattr(analysis_result, 'document_info') and analysis_result.document_info:
                doc_info = analysis_result.document_info
                
                # 检查是否有封面页信息
                if 'cover_page_info' in doc_info:
                    cover_info = doc_info['cover_page_info']
                    logger.info(f"从document_info中获取封面页信息: {cover_info}")
                    return cover_info
                
                # 尝试从其他字段中获取部分信息
                if 'title' in doc_info or 'author' in doc_info:
                    partial_info = default_cover_info.copy()
                    partial_info['title'] = doc_info.get('title', '')
                    partial_info['author'] = doc_info.get('author', '')
                    return partial_info
            
            # 🔥 新增：从 formatted_json 中提取封面页信息
            if hasattr(analysis_result, 'formatted_json') and analysis_result.formatted_json:
                try:
                    import json
                    formatted_data = json.loads(analysis_result.formatted_json)
                    
                    # 检查document_info中的封面页信息
                    if 'document_info' in formatted_data and isinstance(formatted_data['document_info'], dict):
                        doc_info = formatted_data['document_info']
                        if 'cover_page_info' in doc_info:
                            cover_info = doc_info['cover_page_info']
                            logger.info(f"从formatted_json中获取封面页信息: {cover_info}")
                            return cover_info
                    
                    # 检查是否有直接的封面页信息
                    if 'cover_page_info' in formatted_data:
                        cover_info = formatted_data['cover_page_info']
                        logger.info(f"从formatted_json直接获取封面页信息: {cover_info}")
                        return cover_info
                        
                except Exception as e:
                    logger.warning(f"从formatted_json解析封面页信息失败: {e}")
            
            # 如果所有方法都失败，返回默认值
            logger.warning("无法从分析结果中提取封面页信息，返回默认值")
            return default_cover_info
            
        except Exception as e:
            logger.error(f"提取封面页信息失败: {str(e)}")
            return {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': '',
                'error': str(e)
            }


# 全局任务管理器实例
task_manager = TaskManager() 
