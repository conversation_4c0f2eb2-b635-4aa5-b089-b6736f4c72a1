// 用户相关类型
export interface User {
  user_id: string
  username: string
  email: string
  full_name?: string
  check_balance: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface UserProfile {
  id: number
  user_id: number
  full_name?: string
  phone?: string
  avatar?: string
  bio?: string
  preferences: Record<string, any>
}

// 文档相关类型
export interface Document {
  document_id: string
  task_id: string
  filename: string
  file_size: number
  page_count?: number
  word_count?: number
  created_at: string
  status: string
}

export interface DocumentIssue {
  id: string
  type: 'error' | 'warning' | 'suggestion'
  category: string
  title: string
  description: string
  location: {
    page?: number
    paragraph?: number
    line?: number
  }
  severity: 'low' | 'medium' | 'high' | 'critical'
  suggestion?: string
  auto_fixable: boolean
}

export interface AnalysisSummary {
  errors: number;
  warnings: number;
  suggestions: number;
  score: number;
}

export interface DocumentStatistics {
  pages: number
  words: number
  paragraphs: number
  charts: number
  references: number
  tables: number
  images: number
  formulas: number
  spaces: number
  footnotes: number
  endnotes: number
}

export interface DetectionInfo {
  type: string
  standard: string
  start_time: string
  end_time: string
  duration: string
}

export interface DetectionHistoryItem {
  id: string
  version: string
  date: string
  current: boolean
}

export interface DocumentDetail {
  // 基本信息
  id: string
  filename: string
  task_id: string
  file_size: number
  upload_time: string
  status: string
  
  // 分析结果（从PaperCheckResult获取）
  analysis: {
    score: number
    errors: number
    warnings: number
    suggestions: number
    compliance_status?: string
  }
  
  // 问题列表（从API获取）
  issues: DocumentProblem[]
  
  // 文档统计（从DocumentContent获取）
  statistics: {
    pages: number
    words: number
    paragraphs: number
    charts: number
    references: number
    tables: number
    images: number
    formulas: number
    spaces: number
    footnotes: number
    endnotes: number
  }
  
  // 检测信息
  detection: {
    type: string
    standard: string
    start_time: string
    end_time: string
    duration: string
  }
  
  // 检测历史（可选）
  detectionHistory?: DetectionHistoryItem[]
  
  // 可选的扩展数据
  document_id?: string  // 新增：实际的文档ID
  document_info?: Document
  paper_check_result?: PaperCheckResult
  document_content?: DocumentContent
  document_images?: DocumentImage[]
  task_detail?: TaskDetail  // 新增：任务详情
}

// 任务相关类型
export interface Task {
  task_id: string
  user_id: string
  filename: string
  file_path: string
  file_size: number
  task_type: string
  analysis_options: any
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  created_at: string
  started_at?: string
  completed_at?: string
  processing_time?: number
  error_message?: string
  result: any
  updated_at: string
}

export interface TaskProgress {
  task_id: string
  progress: number
  status: string
  current_step?: string
  message?: string
  updated_at: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp: number
  request_id: string
}

// 认证相关类型
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  user: User
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  full_name?: string
  phone?: string
}

// 通用类型
export interface PaginationParams {
  page?: number
  limit?: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  per_page: number
  pages: number
}

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  message?: string
}

export interface FormRules {
  [key: string]: ValidationRule[]
} 

export interface SystemStats {
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
  pending_tasks: number
  processing_tasks: number
  total_documents: number
  total_problems: number
  avg_compliance_score?: number | null
}

export interface ProblemStats {
  severity: string
  count: number
}

// 新增：文档问题接口
export interface DocumentProblem {
  problem_id: string
  category: string
  problem_type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  position?: number
  range_start?: number
  range_end?: number
  page_number?: number
  suggestion?: string
  auto_fixable: boolean
  element_id?: string
}

// 新增：论文检测结果接口
export interface PaperCheckResult {
  result_id: string
  paper_standard: string
  overall_score: number
  compliance_status: 'compliant' | 'partially_compliant' | 'non_compliant' | 'unknown'
  total_problems: number
  major_problems: number
  minor_problems: number
  detailed_results: any
  checked_at: string
}

// 新增：文档内容接口
export interface DocumentContent {
  document_id: string
  title?: string
  author?: string
  abstract?: string
  pages: number
  words: number
  tables: number
  images: number
  content_elements?: Array<{
    element_id: string
    element_type: string
    content: string
    position: number
    page_number?: number
  }>
}

// 新增：文档图片接口
export interface DocumentImage {
  image_id: string
  file_path: string
  original_width: number
  original_height: number
  display_width: number
  display_height: number
  position: number
  page_number?: number
  caption?: string
}

export interface TaskDetail extends Task {
  standard_name?: string; // 新增：检测标准全称
  steps?: Array<{
    step_name: string
    status: 'pending' | 'running' | 'completed' | 'failed'
    started_at?: string
    completed_at?: string
    message?: string
  }>
  options?: Record<string, any>
  log_entries?: Array<{
    timestamp: string
    level: 'info' | 'warning' | 'error'
    message: string
  }>
}

export interface PaymentPlan {
  plan_id: string
  name: string
  price: number
  checks: number
  description: string
}

export interface Order {
  order_id: string
  user_id: string
  plan_id: string
  amount: number
  payment_method: string
  status: 'pending' | 'paid' | 'cancelled' | 'refunded'
  created_at: string
  paid_at?: string
} 