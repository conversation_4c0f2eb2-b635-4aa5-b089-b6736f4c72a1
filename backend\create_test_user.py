#!/usr/bin/env python3
"""
创建测试用户并获取认证token
"""

import requests
import json

def create_test_user():
    """创建测试用户"""
    
    base_url = "http://localhost:8000/api/v1"
    
    # 测试用户数据
    user_data = {
        "username": "test_user",
        "email": "<EMAIL>",
        "password": "test123456",
        "full_name": "测试用户"
    }
    
    try:
        # 1. 尝试注册用户
        print("🚀 尝试注册测试用户...")
        register_response = requests.post(
            f"{base_url}/auth/register",
            json=user_data,
            headers={"Content-Type": "application/json"}
        )
        
        if register_response.status_code == 200:
            print("✅ 用户注册成功")
            register_data = register_response.json()
            token = register_data.get('access_token')
            if token:
                print(f"🔑 获取到token: {token[:20]}...")
                return token
        elif register_response.status_code == 400:
            print("⚠️ 用户可能已存在，尝试登录...")
        else:
            print(f"❌ 注册失败: {register_response.status_code}")
            print(f"响应: {register_response.text}")
        
        # 2. 尝试登录
        print("🚀 尝试登录...")
        login_data = {
            "username": user_data["username"],
            "password": user_data["password"]
        }
        
        # 使用form-data格式
        form_data = f"username={login_data['username']}&password={login_data['password']}"
        login_response = requests.post(
            f"{base_url}/auth/login",
            data=form_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if login_response.status_code == 200:
            print("✅ 用户登录成功")
            login_data = login_response.json()
            token = login_data.get('access_token')
            if token:
                print(f"🔑 获取到token: {token[:20]}...")
                return token
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"响应: {login_response.text}")
            
        return None
        
    except Exception as e:
        print(f"❌ 创建用户失败: {str(e)}")
        return None

def test_authenticated_api(token):
    """使用token测试API"""
    
    task_id = "test_structure_display_001"
    url = f"http://localhost:8000/api/v1/tasks/{task_id}"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        print(f"🚀 使用token测试API: {url}")
        response = requests.get(url, headers=headers)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功")
            
            if data.get('success') and data.get('data'):
                task_data = data['data']
                result = task_data.get('result', {})
                
                print(f"📊 任务ID: {task_data.get('task_id')}")
                print(f"📊 任务状态: {task_data.get('status')}")
                
                if result:
                    structures = result.get('document_structures', [])
                    print(f"📊 文档结构数量: {len(structures)}")
                    
                    if structures:
                        print(f"\n📝 前3个文档结构:")
                        for i, structure in enumerate(structures[:3], 1):
                            name = structure.get('structure_name') or structure.get('name', 'Unknown')
                            type_info = structure.get('type', 'unknown')
                            page = structure.get('page', 'unknown')
                            print(f"  {i}. {name} (类型: {type_info}, 页面: {page})")
                    
                    return True
                else:
                    print("❌ 任务没有结果数据")
            else:
                print("❌ API响应格式错误")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print("响应内容:", response.text)
            
        return False
        
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始创建测试用户和测试API...")
    
    # 创建用户并获取token
    token = create_test_user()
    
    if token:
        print(f"\n🎉 成功获取认证token!")
        print(f"Token: {token}")
        
        # 测试API
        print(f"\n🚀 开始测试API...")
        success = test_authenticated_api(token)
        
        if success:
            print(f"\n🎉 API测试成功！")
            print(f"🔗 前端访问链接: http://localhost:3001/statistics-report/test_structure_display_001")
            print(f"💡 请在浏览器中先登录用户: test_user / test123456")
        else:
            print(f"\n💥 API测试失败！")
    else:
        print(f"\n💥 无法获取认证token！")
