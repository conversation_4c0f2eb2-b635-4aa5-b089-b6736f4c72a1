#!/usr/bin/env python3
"""
测试关键词检测功能
"""

import sys
import os
import re
sys.path.append('.')

def test_keywords_detection():
    """测试关键词检测功能"""

    def _is_keywords_content(text: str, identifiers: list, structure_name: str) -> bool:
        """
        专门检测关键词内容

        检测规则：
        1. 段落以"关键词"或"keywords"等开头
        2. 后面跟着中文或英文冒号
        3. 冒号后面有实际内容（不只是空白）
        """
        # 去除前后空白
        text_stripped = text.strip()

        if structure_name == "中文关键词":
            # 中文关键词检测模式
            # 匹配：关键词：xxx 或 关键字：xxx
            pattern = r'^(关键词|关键字)\s*[：:]\s*(.+)'
            match = re.match(pattern, text_stripped)
            if match:
                keywords_content = match.group(2).strip()
                # 确保冒号后面有实际内容（不只是空白或标点）
                if keywords_content and len(keywords_content) > 2:
                    print(f"   🔍 检测到中文关键词: {text_stripped[:50]}...")
                    return True

        elif structure_name == "英文关键词":
            # 英文关键词检测模式
            # 匹配：Keywords: xxx 或 Key words: xxx 或 Key Words: xxx
            pattern = r'^(keywords?|key\s+words?)\s*[：:]\s*(.+)'
            match = re.match(pattern, text_stripped, re.IGNORECASE)
            if match:
                keywords_content = match.group(2).strip()
                # 确保冒号后面有实际内容
                if keywords_content and len(keywords_content) > 2:
                    print(f"   🔍 检测到英文关键词: {text_stripped[:50]}...")
                    return True

        return False
    
    # 测试用例
    test_cases = [
        # 中文关键词测试用例
        {
            "text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法",
            "identifiers": ["关键词", "关键字"],
            "structure_name": "中文关键词",
            "expected": True
        },
        {
            "text": "关键字：测试；关键词；检测",
            "identifiers": ["关键词", "关键字"],
            "structure_name": "中文关键词",
            "expected": True
        },
        {
            "text": "关键词: 测试内容",
            "identifiers": ["关键词", "关键字"],
            "structure_name": "中文关键词",
            "expected": True
        },
        # 英文关键词测试用例
        {
            "text": "Key words:dance creation;virtual reality (VR); augmented reality (AR); short video transmission; choreographic creation techniques",
            "identifiers": ["keywords", "key words"],
            "structure_name": "英文关键词",
            "expected": True
        },
        {
            "text": "Keywords: test, detection, algorithm",
            "identifiers": ["keywords", "key words"],
            "structure_name": "英文关键词",
            "expected": True
        },
        {
            "text": "KEYWORDS: TEST CONTENT",
            "identifiers": ["keywords", "key words"],
            "structure_name": "英文关键词",
            "expected": True
        },
        # 负面测试用例
        {
            "text": "这是一个普通的段落，不包含关键词标识",
            "identifiers": ["关键词", "关键字"],
            "structure_name": "中文关键词",
            "expected": False
        },
        {
            "text": "This is a normal paragraph without keywords",
            "identifiers": ["keywords", "key words"],
            "structure_name": "英文关键词",
            "expected": False
        }
    ]
    
    print("🧪 关键词检测功能测试")
    print("=" * 60)
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        text = test_case["text"]
        identifiers = test_case["identifiers"]
        structure_name = test_case["structure_name"]
        expected = test_case["expected"]
        
        print(f"\n📝 测试用例 {i}: {structure_name}")
        print(f"   文本: {text[:50]}...")
        
        # 测试关键词检测
        result = _is_keywords_content(text, identifiers, structure_name)
        
        print(f"   期望结果: {expected}")
        print(f"   实际结果: {result}")
        
        if result == expected:
            print("   ✅ 通过")
            passed += 1
        else:
            print("   ❌ 失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，需要检查关键词检测逻辑")

if __name__ == "__main__":
    test_keywords_detection()
