{"outline": [{"page": 1, "text": "学士学位论文", "type": "standard", "level": 0, "style": "正文", "is_bold": false, "alignment": "center", "font_size": 36.0, "structure_name": "封面", "paragraph_index": 1}, {"page": 2, "text": "河北科技学院本科生毕业设计（论文）任务书", "type": "standard", "level": 1, "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "任务书", "paragraph_index": 0}, {"page": 3, "text": "河北科技学院本科生毕业设计(论文)开题报告", "type": "standard", "level": 1, "style": "开题报告-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "开题报告", "paragraph_index": 0}, {"page": 7, "text": "河北科技学院本科生毕业设计（论文）测试", "type": "non_standard", "level": 2, "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "河北科技学院本科生毕业设计（论文）测试", "paragraph_index": 0}, {"page": 9, "text": "学位论文原创性声明", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "诚信声明", "paragraph_index": 0}, {"page": 9, "text": "学位论文版权使用授权书", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "版权声明", "paragraph_index": 5}, {"page": 10, "text": "河北科技学院本科生毕业设计（论文）测试2", "type": "non_standard", "level": 2, "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "河北科技学院本科生毕业设计（论文）测试2", "paragraph_index": 0}, {"page": 11, "text": "摘　　要", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "中文摘要", "paragraph_index": 0}, {"page": 11, "text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "type": "standard", "level": 2, "style": "正文", "is_bold": false, "alignment": "justify", "font_size": 12.0, "structure_name": "中文关键词", "paragraph_index": 2}, {"page": 12, "text": "ABSTRACT", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "英文摘要", "paragraph_index": 0}, {"page": 12, "text": "Key words:dance creation;virtual reality (VR); augmented reality (AR); short video communication; choreography and creation techniques", "type": "standard", "level": 2, "style": "正文", "is_bold": true, "alignment": "justify", "font_size": 12.0, "structure_name": "英文关键词", "paragraph_index": 2}, {"page": 13, "text": "目　　录", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "目录", "paragraph_index": 0}, {"page": 15, "text": "绪论", "type": "standard", "level": 1, "style": "标题 1", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "正文", "paragraph_index": 0}, {"page": 32, "text": "结　　论", "type": "non_standard", "level": 2, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "结　　论", "paragraph_index": 0}, {"page": 33, "text": "参考文献", "type": "standard", "level": 1, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "参考文献", "paragraph_index": 0}, {"page": 34, "text": "列表啊", "type": "non_standard", "level": 2, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "列表啊", "paragraph_index": 0}, {"page": 35, "text": "致　　谢", "type": "standard", "level": 1, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "致谢", "paragraph_index": 0}, {"page": 36, "text": "列表啊2", "type": "non_standard", "level": 2, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "列表啊2", "paragraph_index": 0}], "document_info": {"cover_page_info": {"major": "舞蹈编导", "title": "新媒体时代舞蹈编导创作手法的创新研究", "author": "张三", "degree_type": "学士学位论文"}}, "analysis_result": {"structure_analysis": {"outline": [{"page": 1, "text": "学士学位论文", "type": "standard", "level": 0, "style": "正文", "is_bold": false, "alignment": "center", "font_size": 36.0, "structure_name": "封面", "paragraph_index": 1}, {"page": 2, "text": "河北科技学院本科生毕业设计（论文）任务书", "type": "standard", "level": 1, "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "任务书", "paragraph_index": 0}, {"page": 3, "text": "河北科技学院本科生毕业设计(论文)开题报告", "type": "standard", "level": 1, "style": "开题报告-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "开题报告", "paragraph_index": 0}, {"page": 7, "text": "河北科技学院本科生毕业设计（论文）测试", "type": "non_standard", "level": 2, "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "河北科技学院本科生毕业设计（论文）测试", "paragraph_index": 0}, {"page": 9, "text": "学位论文原创性声明", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "诚信声明", "paragraph_index": 0}, {"page": 9, "text": "学位论文版权使用授权书", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "版权声明", "paragraph_index": 5}, {"page": 10, "text": "河北科技学院本科生毕业设计（论文）测试2", "type": "non_standard", "level": 2, "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "河北科技学院本科生毕业设计（论文）测试2", "paragraph_index": 0}, {"page": 11, "text": "摘　　要", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "中文摘要", "paragraph_index": 0}, {"page": 11, "text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "type": "standard", "level": 2, "style": "正文", "is_bold": false, "alignment": "justify", "font_size": 12.0, "structure_name": "中文关键词", "paragraph_index": 2}, {"page": 12, "text": "ABSTRACT", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "英文摘要", "paragraph_index": 0}, {"page": 12, "text": "Key words:dance creation;virtual reality (VR); augmented reality (AR); short video communication; choreography and creation techniques", "type": "standard", "level": 2, "style": "正文", "is_bold": true, "alignment": "justify", "font_size": 12.0, "structure_name": "英文关键词", "paragraph_index": 2}, {"page": 13, "text": "目　　录", "type": "standard", "level": 1, "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "目录", "paragraph_index": 0}, {"page": 15, "text": "绪论", "type": "standard", "level": 1, "style": "标题 1", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "正文", "paragraph_index": 0}, {"page": 32, "text": "结　　论", "type": "non_standard", "level": 2, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "结　　论", "paragraph_index": 0}, {"page": 33, "text": "参考文献", "type": "standard", "level": 1, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "参考文献", "paragraph_index": 0}, {"page": 34, "text": "列表啊", "type": "non_standard", "level": 2, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "列表啊", "paragraph_index": 0}, {"page": 35, "text": "致　　谢", "type": "standard", "level": 1, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "致谢", "paragraph_index": 0}, {"page": 36, "text": "列表啊2", "type": "non_standard", "level": 2, "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "structure_name": "列表啊2", "paragraph_index": 0}], "sections": [{"name": "封面", "page": 1, "type": "standard", "status": "present", "content": {"text": "学士学位论文", "style": "正文", "is_bold": false, "alignment": "center", "font_size": 36.0, "paragraph_index": 1}, "required": true, "identifiers_matched": ["学士学位论文", "题目", "学生", "姓名", "指导教师"]}, {"name": "任务书", "page": 2, "type": "standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）任务书", "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["任务书", "毕业设计任务书"]}, {"name": "开题报告", "page": 3, "type": "standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计(论文)开题报告", "style": "开题报告-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["开题报告"]}, {"name": "河北科技学院本科生毕业设计（论文）测试", "page": 7, "type": "non_standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）测试", "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "诚信声明", "page": 9, "type": "standard", "status": "present", "content": {"text": "学位论文原创性声明", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["诚信声明", "承诺书", "原创性声明"]}, {"name": "版权声明", "page": 9, "type": "standard", "status": "present", "content": {"text": "学位论文版权使用授权书", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 5}, "required": true, "identifiers_matched": ["版权声明", "版权", "知识产权", "使用授权书", "学位论文版权使用授权书"]}, {"name": "河北科技学院本科生毕业设计（论文）测试2", "page": 10, "type": "non_standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）测试2", "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "中文摘要", "page": 11, "type": "standard", "status": "present", "content": {"text": "摘　　要", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["摘要", "概要"]}, {"name": "中文关键词", "page": 11, "type": "standard", "status": "present", "content": {"text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "style": "正文", "is_bold": false, "alignment": "justify", "font_size": 12.0, "paragraph_index": 2}, "required": true, "identifiers_matched": ["关键词", "关键字"]}, {"name": "英文摘要", "page": 12, "type": "standard", "status": "present", "content": {"text": "ABSTRACT", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["abstract"]}, {"name": "英文关键词", "page": 12, "type": "standard", "status": "present", "content": {"text": "Key words:dance creation;virtual reality (VR); augmented reality (AR); short video communication; choreography and creation techniques", "style": "正文", "is_bold": true, "alignment": "justify", "font_size": 12.0, "paragraph_index": 2}, "required": true, "identifiers_matched": ["keywords", "key words"]}, {"name": "目录", "page": 13, "type": "standard", "status": "present", "content": {"text": "目　　录", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0, "detection_method": "toc_style", "toc_entries_count": 56}, "required": true, "identifiers_matched": ["目录", "contents"]}, {"name": "正文", "page": 15, "type": "standard", "status": "present", "content": {"text": "绪论", "style": "标题 1", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["第", "章", "引言", "绪论"]}, {"name": "结　　论", "page": 32, "type": "non_standard", "status": "present", "content": {"text": "结　　论", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "参考文献", "page": 33, "type": "standard", "status": "present", "content": {"text": "参考文献", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["参考文献", "reference", "参考资料"]}, {"name": "列表啊", "page": 34, "type": "non_standard", "status": "present", "content": {"text": "列表啊", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "致谢", "page": 35, "type": "standard", "status": "present", "content": {"text": "致　　谢", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["致谢", "谢辞"]}, {"name": "列表啊2", "page": 36, "type": "non_standard", "status": "present", "content": {"text": "列表啊2", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "附录", "type": "standard", "status": "missing", "required": true}]}}, "detection_standard": "hbkj_bachelor_2024", "document_structures": [{"name": "封面", "page": 1, "type": "standard", "status": "present", "content": {"text": "学士学位论文", "style": "正文", "is_bold": false, "alignment": "center", "font_size": 36.0, "paragraph_index": 1}, "required": true, "identifiers_matched": ["学士学位论文", "题目", "学生", "姓名", "指导教师"]}, {"name": "任务书", "page": 2, "type": "standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）任务书", "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["任务书", "毕业设计任务书"]}, {"name": "开题报告", "page": 3, "type": "standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计(论文)开题报告", "style": "开题报告-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["开题报告"]}, {"name": "河北科技学院本科生毕业设计（论文）测试", "page": 7, "type": "non_standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）测试", "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "诚信声明", "page": 9, "type": "standard", "status": "present", "content": {"text": "学位论文原创性声明", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["诚信声明", "承诺书", "原创性声明"]}, {"name": "版权声明", "page": 9, "type": "standard", "status": "present", "content": {"text": "学位论文版权使用授权书", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 5}, "required": true, "identifiers_matched": ["版权声明", "版权", "知识产权", "使用授权书", "学位论文版权使用授权书"]}, {"name": "河北科技学院本科生毕业设计（论文）测试2", "page": 10, "type": "non_standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）测试2", "style": "任务书-标题", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "中文摘要", "page": 11, "type": "standard", "status": "present", "content": {"text": "摘　　要", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["摘要", "概要"]}, {"name": "中文关键词", "page": 11, "type": "standard", "status": "present", "content": {"text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "style": "正文", "is_bold": false, "alignment": "justify", "font_size": 12.0, "paragraph_index": 2}, "required": true, "identifiers_matched": ["关键词", "关键字"]}, {"name": "英文摘要", "page": 12, "type": "standard", "status": "present", "content": {"text": "ABSTRACT", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["abstract"]}, {"name": "英文关键词", "page": 12, "type": "standard", "status": "present", "content": {"text": "Key words:dance creation;virtual reality (VR); augmented reality (AR); short video communication; choreography and creation techniques", "style": "正文", "is_bold": true, "alignment": "justify", "font_size": 12.0, "paragraph_index": 2}, "required": true, "identifiers_matched": ["keywords", "key words"]}, {"name": "目录", "page": 13, "type": "standard", "status": "present", "content": {"text": "目　　录", "style": "标题 1- 非目录", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0, "detection_method": "toc_style", "toc_entries_count": 56}, "required": true, "identifiers_matched": ["目录", "contents"]}, {"name": "正文", "page": 15, "type": "standard", "status": "present", "content": {"text": "绪论", "style": "标题 1", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["第", "章", "引言", "绪论"]}, {"name": "结　　论", "page": 32, "type": "non_standard", "status": "present", "content": {"text": "结　　论", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "参考文献", "page": 33, "type": "standard", "status": "present", "content": {"text": "参考文献", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["参考文献", "reference", "参考资料"]}, {"name": "列表啊", "page": 34, "type": "non_standard", "status": "present", "content": {"text": "列表啊", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "致谢", "page": 35, "type": "standard", "status": "present", "content": {"text": "致　　谢", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": true, "identifiers_matched": ["致谢", "谢辞"]}, {"name": "列表啊2", "page": 36, "type": "non_standard", "status": "present", "content": {"text": "列表啊2", "style": "标题1 - 目录 - 非编号", "is_bold": false, "alignment": "center", "font_size": 15.0, "paragraph_index": 0}, "required": false, "identifiers_matched": []}, {"name": "附录", "type": "standard", "status": "missing", "required": true}]}