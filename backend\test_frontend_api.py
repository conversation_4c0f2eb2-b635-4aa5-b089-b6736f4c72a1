#!/usr/bin/env python3
"""
测试前端API调用
"""

import requests
import json

def test_task_api():
    """测试任务API"""
    
    task_id = "test_structure_display_001"
    url = f"http://localhost:8000/api/v1/tasks/{task_id}"
    
    # 创建一个简单的认证token（如果需要的话）
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        print(f"🚀 测试API调用: {url}")
        response = requests.get(url, headers=headers)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功")
            
            # 检查数据结构
            if data.get('success') and data.get('data'):
                task_data = data['data']
                result = task_data.get('result', {})
                
                print(f"📊 任务ID: {task_data.get('task_id')}")
                print(f"📊 任务状态: {task_data.get('status')}")
                print(f"📊 文件名: {task_data.get('filename')}")
                
                if result:
                    structures = result.get('document_structures', [])
                    outline = result.get('outline', [])
                    print(f"📊 文档结构数量: {len(structures)}")
                    print(f"📊 大纲条目数量: {len(outline)}")
                    
                    # 显示前3个结构
                    if structures:
                        print(f"\n📝 前3个文档结构:")
                        for i, structure in enumerate(structures[:3], 1):
                            name = structure.get('structure_name') or structure.get('name', 'Unknown')
                            type_info = structure.get('type', 'unknown')
                            page = structure.get('page', 'unknown')
                            print(f"  {i}. {name} (类型: {type_info}, 页面: {page})")
                else:
                    print("❌ 任务没有结果数据")
            else:
                print("❌ API响应格式错误")
                print(f"响应内容: {data}")
                
        elif response.status_code == 401:
            print("❌ 认证失败 - 需要登录")
            print("响应内容:", response.text)
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print("响应内容:", response.text)
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

if __name__ == "__main__":
    test_task_api()
