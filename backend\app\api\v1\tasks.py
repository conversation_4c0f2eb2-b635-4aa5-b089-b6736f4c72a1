"""
Word文档分析服务 - 任务管理API
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, UploadFile, File
from pydantic import BaseModel
import uuid
import os
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.task import Task, TaskCreate, TaskUpdate, TaskStatus, TaskType
from app.database import crud
from app.database.session import get_db
from app.core.exceptions import TaskException
from app.tasks.manager import task_manager
from app.core.logging import logger
from app.core.response import success_response, error_response
from app.security import get_current_user_id
from app.core.config import DETECTION_STANDARD_NAMES
from app.core.config import DETECTION_STANDARD_NAMES

router = APIRouter()


class TaskCreateRequest(BaseModel):
    """创建任务请求模型"""
    task_type: TaskType
    file_path: Optional[str] = None
    options: Optional[dict] = None
    priority: Optional[int] = 0
    description: Optional[str] = None


class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    message: str
    status: TaskStatus


class Pagination(BaseModel):
    """分页信息模型"""
    page: int
    limit: int
    total: int
    total_pages: int


class PaginatedTaskResponse(BaseModel):
    """分页任务响应模型"""
    tasks: List[Task]
    pagination: Pagination


@router.post("/", status_code=201)
async def create_task(
    task_request: TaskCreateRequest,
    background_tasks: BackgroundTasks,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    创建新的文档分析任务
    
    - **task_type**: 任务类型 (paper_check, content_analysis, format_check)
    - **file_path**: 文档文件路径
    - **options**: 任务选项配置
    - **priority**: 任务优先级 (0-10, 数字越大优先级越高)
    - **description**: 任务描述
    """
    try:
        # 生成任务ID
        task_id = f"task_{uuid.uuid4().hex}"
        
        # 创建任务数据
        task_data = TaskCreate(
            task_id=task_id,
            task_type=task_request.task_type,
            file_path=task_request.file_path,
            filename=task_request.file_path.split('/')[-1] if task_request.file_path else "unknown.docx",
            file_size=1024,  # 设置默认文件大小1KB
            analysis_options=task_request.options or {},
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
        
        # 保存到数据库
        created_task = await crud.create_task(session, task_data)
        
        # 添加到后台任务队列
        background_tasks.add_task(task_manager.process_task, task_id)
        
        logger.info(f"创建任务成功: {task_id}", task_type=task_request.task_type.value)
        
        task_response = {
            "task_id": task_id,
            "message": "任务创建成功",
            "status": TaskStatus.PENDING.value
        }
        
        return success_response(data=task_response, message="任务创建成功", code=201)
        
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        return error_response(message=f"创建任务失败: {str(e)}", code=500)


@router.post("/upload", response_model=TaskResponse, status_code=201)
async def create_task_with_upload(
    file: UploadFile = File(...),
    task_type: TaskType = TaskType.PAPER_CHECK,
    options: Optional[str] = None,
    priority: int = 0,
    description: Optional[str] = None,
    background_tasks: BackgroundTasks = None,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    上传文档并创建分析任务
    
    - **file**: 上传的Word文档文件
    - **task_type**: 任务类型
    - **options**: JSON格式的任务选项
    - **priority**: 任务优先级
    - **description**: 任务描述
    """
    try:
        # 验证文件类型
        if not file.filename.endswith(('.doc', '.docx')):
            raise HTTPException(status_code=400, detail="只支持Word文档格式 (.doc, .docx)")
        
        # 生成任务ID和文件路径
        task_id = f"task_{uuid.uuid4().hex}"
        file_extension = file.filename.split('.')[-1]
        file_path = f"data/uploads/{task_id}.{file_extension}"
        
        # 保存上传的文件
        import os
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 解析选项
        task_options = {}
        if options:
            import json
            try:
                task_options = json.loads(options)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="选项格式错误，请使用有效的JSON格式")
        
        # 创建任务数据
        task_data = TaskCreate(
            task_id=task_id,
            task_type=task_type,
            file_path=file_path,
            filename=file.filename,
            file_size=len(content),
            analysis_options=task_options,
            priority=priority,
            description=description or f"分析文档: {file.filename}",
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
        
        # 保存到数据库
        created_task = await crud.create_task(session, task_data)
        
        # 添加到后台任务队列
        background_tasks.add_task(task_manager.process_task, task_id)
        
        logger.info(f"上传文档并创建任务成功: {task_id}", 
                   filename=file.filename, 
                   file_size=len(content))
        
        return TaskResponse(
            task_id=task_id,
            message=f"文档上传成功，任务已创建",
            status=TaskStatus.PENDING
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文档创建任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传文档创建任务失败: {str(e)}")


@router.get("/{task_id}")
async def get_task_status(task_id: str, user_id: str = Depends(get_current_user_id), session: AsyncSession = Depends(get_db)):
    """
    获取指定任务的状态和详细信息
    
    - **task_id**: 任务ID
    """
    try:
        task = await crud.get_task(session, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务未找到")
        
        # 🔥 新增：集成进度跟踪器获取实时进度
        from app.tasks.progress import get_progress_tracker
        progress_tracker = await get_progress_tracker()
        
        # 获取实时进度信息
        live_progress = await progress_tracker.get_task_progress(task_id)
        
        # 🔥 关键修复：使用正确的转换函数处理任务结果
        from app.tasks.manager import task_manager
        
        task_data = task.model_dump()
        
        # 🔥 修复：动态添加检测标准全称 (standard_name)
        standard_key = "default" # 默认值
        # 优先从 analysis_options 获取 detection_standard（前端传递的字段名）
        if task_data.get("analysis_options") and task_data["analysis_options"].get("detection_standard"):
            standard_key = task_data["analysis_options"]["detection_standard"]
        # 兼容旧的 standard 字段名
        elif task_data.get("analysis_options") and task_data["analysis_options"].get("standard"):
            standard_key = task_data["analysis_options"]["standard"]
        # 其次从 result 获取
        elif task_data.get("result") and task_data["result"].get("detection_standard"):
            standard_key = task_data["result"]["detection_standard"]

        task_data["standard_name"] = DETECTION_STANDARD_NAMES.get(standard_key, DETECTION_STANDARD_NAMES["default"])

        # 🔥 新增：如果有实时进度，优先使用实时数据
        if live_progress:
            task_data.update({
                "progress": live_progress.get("overall_progress", task.progress),
                "status": live_progress.get("status", task.status.value),
                "current_step": live_progress.get("current_step"),
                "steps": live_progress.get("steps", {}),
                "estimated_completion": live_progress.get("estimated_completion"),
                "live_progress": True  # 标记这是实时进度
            })
            logger.info(f"返回实时进度数据: {task_id}")
        else:
            # 如果没有实时进度，使用数据库中的数据
            task_data["live_progress"] = False
            logger.info(f"返回数据库进度数据: {task_id}")
        
        # 如果任务有结果且状态为已完成，应用数据转换
        if task_data.get('result') and task.status == TaskStatus.COMPLETED:
            try:
                # 🔥 修复：使用_convert_dict_to_frontend_format处理嵌套数据
                converted_result = task_manager._convert_dict_to_frontend_format(task_data['result'])
                task_data['result'] = converted_result
                logger.info(f"任务结果已转换为前端格式: {task_id}")
            except Exception as e:
                logger.warning(f"转换任务结果失败: {task_id} - {str(e)}")
                # 如果转换失败，保持原始结果
        
        # 返回标准响应格式
        return success_response(
            data=task_data,
            message="获取任务信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务信息失败: {str(e)}")
        return error_response(message=f"获取任务信息失败: {str(e)}", code=500)


@router.get("/{task_id}/status")
async def get_task_status_only(task_id: str, session: AsyncSession = Depends(get_db)):
    """
    获取指定任务的状态信息（简化版本）
    
    - **task_id**: 任务ID
    """
    try:
        task = await crud.get_task(session, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务未找到")
        
        # 🔥 新增：集成进度跟踪器获取实时进度
        from app.tasks.progress import get_progress_tracker
        progress_tracker = await get_progress_tracker()
        
        # 获取实时进度信息
        live_progress = await progress_tracker.get_task_progress(task_id)
        
        if live_progress:
            # 使用实时进度数据
            status_data = {
                "task_id": task_id,
                "status": live_progress.get("status", task.status.value),
                "progress": live_progress.get("overall_progress", task.progress),
                "created_at": task.created_at,
                "updated_at": task.updated_at,
                "current_step": live_progress.get("current_step"),
                "steps": live_progress.get("steps", {}),
                "live_progress": True
            }
        else:
            # 使用数据库数据
            status_data = {
                "task_id": task.task_id,
                "status": task.status.value,
                "progress": task.progress,
                "created_at": task.created_at,
                "updated_at": task.updated_at,
                "live_progress": False
            }
        
        return success_response(data=status_data, message="获取任务状态成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        return error_response(message=f"获取任务状态失败: {str(e)}", code=500)


@router.post("/{task_id}/cancel")
async def cancel_task_post(task_id: str, background_tasks: BackgroundTasks, session: AsyncSession = Depends(get_db)):
    """
    取消指定的任务
    
    - **task_id**: 任务ID
    """
    try:
        task = await crud.get_task(session, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务未找到")
        
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            raise HTTPException(status_code=400, detail="任务已完成，无法取消")
        
        # 更新任务状态为已取消
        await crud.update_task(session, task_id, TaskUpdate(
            status=TaskStatus.CANCELLED,
            error_message="用户主动取消任务"
        ))
        
        # 从任务队列中移除（如果可能）
        background_tasks.add_task(task_manager.cancel_task, task_id)
        
        logger.info(f"取消任务: {task_id}")
        
        return success_response(
            data={"task_id": task_id, "status": "cancelled"},
            message="任务已取消"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/")
async def get_task_list(
    status: Optional[TaskStatus] = Query(None, description="按任务状态筛选"),
    task_type: Optional[TaskType] = Query(None, description="按任务类型筛选"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取当前用户的任务列表（支持分页和筛选）
    
    - **status**: 任务状态筛选
    - **task_type**: 任务类型筛选
    - **page**: 页码
    - **limit**: 每页数量
    """
    try:
        skip = (page - 1) * limit
        
        # 🔥 修复：获取当前用户的任务列表
        tasks = await crud.get_user_tasks(session, user_id=user_id, skip=skip, limit=limit, status=status, task_type=task_type)
        total_tasks = await crud.count_user_tasks(session, user_id=user_id, status=status, task_type=task_type)
        
        total_pages = (total_tasks + limit - 1) // limit
        
        pagination = Pagination(
            page=page,
            limit=limit,
            total=total_tasks,
            total_pages=total_pages
        )
        
        response_data = {
            "tasks": [task.model_dump() for task in tasks],
            "pagination": pagination.model_dump()
        }
        
        return success_response(data=response_data, message="获取任务列表成功")
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        return error_response(message=f"获取任务列表失败: {str(e)}", code=500)


@router.put("/{task_id}/status", response_model=TaskResponse)
async def update_task_status(
    task_id: str,
    status: TaskStatus,
    message: Optional[str] = None,
    session: AsyncSession = Depends(get_db)
):
    """
    更新任务状态
    
    - **task_id**: 任务ID
    - **status**: 新的任务状态
    - **message**: 状态更新说明
    """
    try:
        task = await crud.get_task(session, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务未找到")
        
        update_data = TaskUpdate(
            status=status,
            error_message=message if status == TaskStatus.FAILED else None
        )
        
        updated_task = await crud.update_task(session, task_id, update_data)
        
        logger.info(f"更新任务状态: {task_id} -> {status.value}")
        
        return TaskResponse(
            task_id=task_id,
            message=message or f"任务状态已更新为 {status.value}",
            status=status
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新任务状态失败: {str(e)}")


@router.delete("/{task_id}", status_code=200)
async def cancel_task(task_id: str, background_tasks: BackgroundTasks, user_id: str = Depends(get_current_user_id), session: AsyncSession = Depends(get_db)):
    """
    删除任务（真正删除记录和文件）
    
    - **task_id**: 任务ID
    """
    try:
        task = await crud.get_task(session, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务未找到")
        
        # 验证任务是否属于当前用户
        if hasattr(task, 'user_id') and task.user_id != user_id:
            raise HTTPException(status_code=403, detail="无权限删除此任务")
        
        # 如果任务正在进行中，先取消
        if task.status == TaskStatus.PROCESSING:
            background_tasks.add_task(task_manager.cancel_task, task_id)
            # 等待一小段时间让任务停止
            import asyncio
            await asyncio.sleep(1)
        
        # 🔥 关键修复：检查文件引用计数
        file_path = task.file_path
        should_delete_file = False
        
        if file_path and os.path.exists(file_path):
            # 检查是否还有其他任务引用同一个文件
            try:
                from sqlalchemy import text
                result = await session.execute(
                    text("SELECT COUNT(*) as count FROM tasks WHERE file_path = :file_path AND task_id != :task_id"),
                    {"file_path": file_path, "task_id": task_id}
                )
                other_references = result.fetchone().count
                
                if other_references == 0:
                    should_delete_file = True
                    logger.info(f"文件 {file_path} 没有其他引用，将被删除")
                else:
                    logger.info(f"文件 {file_path} 还有 {other_references} 个其他引用，不删除物理文件")
                    
            except Exception as e:
                logger.error(f"检查文件引用失败: {str(e)}，为安全起见不删除文件")
                should_delete_file = False
        
        # 只有当没有其他引用时才删除物理文件
        if should_delete_file:
            try:
                os.remove(file_path)
                logger.info(f"删除文件: {file_path}")
            except Exception as e:
                logger.warning(f"删除文件失败: {file_path}, 错误: {str(e)}")
        
        # 由于数据库设置了ON DELETE CASCADE，删除任务时会自动删除相关文档记录
        # 我们只需要删除任务记录即可
        
        # 真正删除任务记录
        try:
            deleted = await crud.delete_task(session, task_id)
            if not deleted:
                logger.error(f"删除任务记录失败: {task_id} - 返回值为False")
                raise HTTPException(status_code=500, detail="删除任务记录失败：任务可能不存在或已被删除")
        except Exception as e:
            logger.error(f"删除任务记录时发生异常: {task_id} - {str(e)}")
            raise HTTPException(status_code=500, detail=f"删除任务记录失败：{str(e)}")
        
        logger.info(f"彻底删除任务: {task_id}")
        
        return success_response(
            data={
                "task_id": task_id,
                "file_deleted": should_delete_file,
                "message": "文件已安全删除" if should_delete_file else "文件仍被其他任务引用，已保留"
            },
            message="任务已彻底删除"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")


@router.post("/batch-delete", status_code=200)
async def batch_delete_tasks(
    task_ids: List[str],
    background_tasks: BackgroundTasks,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    批量删除任务
    
    - **task_ids**: 任务ID列表
    """
    try:
        deleted_count = 0
        failed_tasks = []
        file_operations = []
        
        for task_id in task_ids:
            try:
                task = await crud.get_task(session, task_id)
                if not task:
                    failed_tasks.append({"task_id": task_id, "reason": "任务未找到"})
                    continue
                
                # 验证任务是否属于当前用户
                if hasattr(task, 'user_id') and task.user_id != user_id:
                    failed_tasks.append({"task_id": task_id, "reason": "无权限删除此任务"})
                    continue
                
                # 如果任务正在进行中，先取消
                if task.status == TaskStatus.PROCESSING:
                    background_tasks.add_task(task_manager.cancel_task, task_id)
                
                # 🔥 关键修复：检查文件引用计数
                file_path = task.file_path
                should_delete_file = False
                
                if file_path and os.path.exists(file_path):
                    # 检查是否还有其他任务引用同一个文件（排除当前批量删除的任务）
                    try:
                        from sqlalchemy import text
                        placeholders = ','.join([f':task_id_{i}' for i in range(len(task_ids))])
                        params = {f'task_id_{i}': tid for i, tid in enumerate(task_ids)}
                        params['file_path'] = file_path
                        
                        result = await session.execute(
                            text(f"SELECT COUNT(*) as count FROM tasks WHERE file_path = :file_path AND task_id NOT IN ({placeholders})"),
                            params
                        )
                        other_references = result.fetchone().count
                        
                        if other_references == 0:
                            should_delete_file = True
                            logger.info(f"文件 {file_path} 没有其他引用，将被删除")
                        else:
                            logger.info(f"文件 {file_path} 还有 {other_references} 个其他引用，不删除物理文件")
                            
                    except Exception as e:
                        logger.error(f"检查文件引用失败: {str(e)}，为安全起见不删除文件")
                        should_delete_file = False
                
                # 只有当没有其他引用时才删除物理文件
                if should_delete_file:
                    try:
                        os.remove(file_path)
                        logger.info(f"删除文件: {file_path}")
                        file_operations.append({"file": file_path, "action": "deleted"})
                    except Exception as e:
                        logger.warning(f"删除文件失败: {file_path}, 错误: {str(e)}")
                        file_operations.append({"file": file_path, "action": "delete_failed", "error": str(e)})
                else:
                    file_operations.append({"file": file_path, "action": "preserved", "reason": "has_references"})
                
                # 删除任务记录
                deleted = await crud.delete_task(session, task_id)
                if deleted:
                    deleted_count += 1
                    logger.info(f"删除任务: {task_id}")
                else:
                    failed_tasks.append({"task_id": task_id, "reason": "删除记录失败"})
                    
            except Exception as e:
                logger.error(f"删除任务 {task_id} 失败: {str(e)}")
                failed_tasks.append({"task_id": task_id, "reason": f"异常: {str(e)}"})
        
        logger.info(f"批量删除完成: 成功 {deleted_count} 个，失败 {len(failed_tasks)} 个")
        
        return success_response(
            data={
                "deleted_count": deleted_count,
                "failed_count": len(failed_tasks),
                "failed_tasks": failed_tasks,
                "file_operations": file_operations
            },
            message=f"批量删除完成: 成功 {deleted_count} 个任务"
        )
        
    except Exception as e:
        logger.error(f"批量删除任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除任务失败: {str(e)}")


@router.get("/{task_id}/progress")
async def get_task_progress(task_id: str, session: AsyncSession = Depends(get_db)):
    """
    获取任务进度信息
    
    - **task_id**: 任务ID
    """
    try:
        task = await crud.get_task(session, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务未找到")
        
        # 🔥 新增：优先使用进度跟踪器的实时数据
        from app.tasks.progress import get_progress_tracker
        progress_tracker = await get_progress_tracker()
        
        # 获取实时进度信息
        live_progress = await progress_tracker.get_task_progress(task_id)
        
        if live_progress:
            # 使用实时进度数据，提供更详细的步骤信息
            progress_data = {
                "task_id": task_id,
                "status": live_progress.get("status"),
                "progress": live_progress.get("overall_progress", 0),
                "current_step": live_progress.get("current_step"),
                "current_step_name": _get_step_name(live_progress.get("current_step")),
                "steps": live_progress.get("steps", {}),
                "created_at": task.created_at,
                "started_at": live_progress.get("start_time") or task.started_at,
                "estimated_completion": live_progress.get("estimated_completion"),
                "processing_time": _calculate_processing_time(live_progress.get("start_time"), task.created_at),
                "metadata": live_progress.get("metadata", {}),
                "live_progress": True
            }
            logger.info(f"返回实时进度信息: {task_id}, 进度: {progress_data['progress']}%")
        else:
            # 回退到数据库数据和估算
            estimated_completion = await _calculate_estimated_completion_time(session, task)
            progress_data = {
                "task_id": task.task_id,
                "status": task.status.value,
                "progress": task.progress,
                "current_step": None,
                "current_step_name": _get_current_step_from_progress(task.progress),
                "steps": {},
                "created_at": task.created_at,
                "started_at": task.started_at,
                "estimated_completion": estimated_completion,
                "processing_time": task.processing_time,
                "live_progress": False
            }
            logger.info(f"返回数据库进度信息: {task_id}, 进度: {progress_data['progress']}%")
        
        return success_response(data=progress_data, message="获取任务进度成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务进度失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务进度失败: {str(e)}")


# =================== 辅助函数 ===================

async def _calculate_estimated_completion_time(session: AsyncSession, task) -> Optional[str]:
    """计算预计完成时间"""
    try:
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            return None
        
        if task.status == TaskStatus.PENDING:
            # 获取队列中前面的任务数量
            pending_count = await crud.count_tasks_before(
                session,
                cutoff_date=task.created_at,
                status=TaskStatus.PENDING
            )
            
            # 获取平均处理时间
            avg_time = await _get_avg_processing_time_for_task_type(session, task.task_type)
            
            if avg_time and pending_count >= 0:
                estimated_seconds = pending_count * avg_time
                estimated_completion = datetime.now() + timedelta(seconds=estimated_seconds)
                return estimated_completion.isoformat()
        
        elif task.status == TaskStatus.PROCESSING:
            # 如果正在处理，基于平均处理时间和当前进度估算
            avg_time = await _get_avg_processing_time_for_task_type(session, task.task_type)
            
            if avg_time and task.progress > 0:
                remaining_progress = 100 - task.progress
                estimated_remaining_time = (remaining_progress / 100) * avg_time
                estimated_completion = datetime.now() + timedelta(seconds=estimated_remaining_time)
                return estimated_completion.isoformat()
        
        return None
        
    except Exception as e:
        logger.error(f"计算预计完成时间失败: {str(e)}")
        return None


async def _get_avg_processing_time_for_task_type(session: AsyncSession, task_type) -> Optional[float]:
    """获取指定任务类型的平均处理时间"""
    try:
        # 获取最近完成的同类型任务
        completed_tasks = await crud.get_recent_completed_tasks(
            session,
            task_type=task_type.value if task_type else None,
            limit=50
        )
        
        if not completed_tasks:
            return 300.0  # 默认5分钟
        
        processing_times = []
        for task_data in completed_tasks:
            if task_data.get('processing_time'):
                processing_times.append(task_data['processing_time'])
        
        if processing_times:
            return sum(processing_times) / len(processing_times)
        
        return 300.0  # 默认5分钟
        
    except Exception as e:
        logger.error(f"获取平均处理时间失败: {str(e)}")
        return 300.0  # 默认5分钟 

def _get_step_name(step_id: Optional[str]) -> Optional[str]:
    """根据步骤ID获取步骤名称"""
    step_names = {
        "step_1": "文档上传验证",
        "step_2": "文档内容提取", 
        "step_3": "格式规则检测",
        "step_4": "生成分析报告"
    }
    return step_names.get(step_id) if step_id else None

def _get_current_step_from_progress(progress: int) -> str:
    """根据进度百分比推断当前步骤"""
    if progress < 25:
        return "文档上传验证"
    elif progress < 50:
        return "文档内容提取"
    elif progress < 75:
        return "格式规则检测"
    elif progress < 100:
        return "生成分析报告"
    else:
        return "分析完成"

def _calculate_processing_time(start_time: Optional[str], created_at: datetime) -> Optional[int]:
    """计算处理时间（秒）"""
    try:
        if start_time:
            from datetime import datetime
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            return int((datetime.utcnow() - start_dt).total_seconds())
        elif created_at:
            return int((datetime.utcnow() - created_at).total_seconds())
        return None
    except Exception:
        return None 