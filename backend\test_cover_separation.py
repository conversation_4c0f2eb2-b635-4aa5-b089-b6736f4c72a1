#!/usr/bin/env python3
"""
封面与论文结构分离测试

测试目标：
1. 验证封面页内容不会重复出现在论文结构中
2. 验证封面结束标志的识别
3. 确保论文结构的准确性
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cover_separation():
    """测试封面与论文结构分离"""
    
    try:
        from app.services.document_processor import DocumentProcessor
        
        # 测试文档路径
        test_doc_path = project_root.parent / "docs" / "test.docx"
        
        if not test_doc_path.exists():
            print(f"❌ 测试文档不存在: {test_doc_path}")
            return False
        
        print(f"🔍 测试封面与论文结构分离")
        print(f"📄 测试文档: {test_doc_path}")
        print("=" * 60)
        
        # 初始化处理器
        processor = DocumentProcessor()
        
        # 分析文档结构
        print("\n📊 开始分析文档结构...")
        structure = processor.analyze_document_structure(str(test_doc_path))
        
        # 1. 检查封面页信息
        print("\n📋 1. 封面页信息")
        print("-" * 30)
        
        cover_info = structure.get('cover_page', {})
        if cover_info.get('has_cover'):
            cover_end = cover_info.get('cover_end_paragraph', 0)
            print(f"✅ 检测到封面页 (结束于段落 {cover_end})")
            
            print("   封面元素:")
            for element in cover_info.get('cover_elements', []):
                print(f"     {element['type']}: {element['text'][:40]}... (段落 {element['paragraph_index']})")
        else:
            print("❌ 未检测到封面页")
        
        # 2. 检查论文结构
        print("\n🏗️ 2. 论文结构分析")
        print("-" * 30)
        
        outline = structure.get('outline', [])
        print(f"   - 论文结构标题数: {len(outline)}")
        
        # 检查是否有封面内容混入论文结构
        cover_end = cover_info.get('cover_end_paragraph', 0)
        cover_content_in_outline = []
        
        for item in outline:
            para_index = item.get('paragraph_index', 0)
            if para_index <= cover_end:
                cover_content_in_outline.append(item)
        
        if cover_content_in_outline:
            print(f"   ❌ 发现 {len(cover_content_in_outline)} 个封面内容混入论文结构:")
            for item in cover_content_in_outline:
                print(f"      段落 {item['paragraph_index']}: {item['text'][:40]}...")
        else:
            print("   ✅ 封面内容已正确排除")
        
        # 3. 显示论文结构的前10个标题
        print("\n📝 3. 论文结构标题 (前10个)")
        print("-" * 30)
        
        for i, item in enumerate(outline[:10], 1):
            text = item.get('text', '')
            text_display = text[:40] + '...' if len(text) > 40 else text
            heading_type = item.get('type', 'unknown')
            para_index = item.get('paragraph_index', 0)
            level = item.get('level', 0)
            
            type_icon = {
                'style': '🎨',
                'centered': '📍',
                'unknown': '❓'
            }.get(heading_type, '❓')
            
            print(f"   {i:2d}. {type_icon} H{level} (段落 {para_index}) | {text_display}")
        
        # 4. 检查关键论文结构
        print("\n🎯 4. 关键论文结构检查")
        print("-" * 30)
        
        key_structures = {
            '声明': any('声明' in item.get('text', '') for item in outline),
            '授权书': any('授权' in item.get('text', '') for item in outline),
            '摘要': any('摘要' in item.get('text', '') for item in outline),
            '目录': any('目录' in item.get('text', '') for item in outline),
            '绪论': any('绪论' in item.get('text', '') for item in outline),
            '参考文献': any('参考文献' in item.get('text', '') for item in outline),
            '致谢': any('致谢' in item.get('text', '') for item in outline)
        }
        
        for structure_name, found in key_structures.items():
            status = "✅" if found else "❌"
            print(f"   {status} {structure_name}")
        
        # 5. 统计分析
        print("\n📊 5. 统计分析")
        print("-" * 30)
        
        style_count = sum(1 for item in outline if item.get('type') == 'style')
        centered_count = sum(1 for item in outline if item.get('type') == 'centered')
        
        print(f"   - Word样式标题: {style_count}")
        print(f"   - 居中识别标题: {centered_count}")
        print(f"   - 封面段落范围: 1-{cover_end}")
        print(f"   - 论文结构起始段落: {cover_end + 1}")
        
        # 6. 保存结果
        print("\n💾 6. 保存测试结果")
        print("-" * 30)
        
        output_file = project_root / "cover_separation_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试结果已保存到: {output_file}")
        
        # 7. 测试结果评估
        print("\n📈 7. 测试结果评估")
        print("-" * 30)
        
        success_criteria = [
            (cover_info.get('has_cover', False), "检测到封面页"),
            (len(cover_content_in_outline) == 0, "封面内容正确排除"),
            (len(outline) > 0, "识别到论文结构"),
            (key_structures['声明'], "找到声明"),
            (key_structures['授权书'], "找到授权书"),
            (cover_end > 0, "确定封面结束位置")
        ]
        
        passed = sum(1 for criteria, _ in success_criteria if criteria)
        total = len(success_criteria)
        
        print(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        print()
        
        for criteria, description in success_criteria:
            status = "✅" if criteria else "❌"
            print(f"   {status} {description}")
        
        return passed >= total * 0.8  # 80%通过率
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 封面与论文结构分离测试")
    print("=" * 60)
    
    success = test_cover_separation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功 - 封面与论文结构正确分离")
        sys.exit(0)
    else:
        print("❌ 测试失败 - 需要进一步优化")
        sys.exit(1)

if __name__ == "__main__":
    main()
