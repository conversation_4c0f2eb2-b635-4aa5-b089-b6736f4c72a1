"""
数据库优化迁移脚本
安全地将现有数据库结构升级到优化版本

使用方法：
python -c "from app.database.migration_script import run_migration; import asyncio; asyncio.run(run_migration())"
"""

import asyncio
from typing import TYPE_CHECKING
from sqlalchemy import text
from datetime import datetime

from app.core.logging import logger
from app.database.session import get_db_session

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession

class DatabaseMigration:
    """数据库迁移管理类"""
    
    def __init__(self, session: "AsyncSession"):
        self.session = session
        self.migration_log = []
    
    async def backup_tables(self):
        """备份现有表数据"""
        logger.info("开始备份现有表数据...")
        
        backup_tables = [
            "users", "tasks", "documents", "content_elements", 
            "images", "paper_check_results", "problems", "orders"
        ]
        
        for table in backup_tables:
            try:
                # 创建备份表
                backup_table = f"{table}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                await self.session.execute(text(f"""
                    CREATE TABLE {backup_table} AS 
                    SELECT * FROM {table}
                """))
                logger.info(f"表 {table} 已备份为 {backup_table}")
                self.migration_log.append(f"BACKUP: {table} -> {backup_table}")
            except Exception as e:
                logger.warning(f"备份表 {table} 失败: {e}")
    
    async def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        result = await self.session.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = :table_name
            )
        """), {"table_name": table_name})
        return result.scalar()
    
    async def add_missing_columns(self):
        """添加缺失的列"""
        logger.info("开始添加缺失的列...")
        
        column_additions = [
            # paper_check_results表缺失的列
            {
                "table": "paper_check_results",
                "column": "paper_standard",
                "definition": "VARCHAR(100) DEFAULT 'undergraduate'"
            },
            {
                "table": "paper_check_results",
                "column": "total_problems",
                "definition": "INTEGER DEFAULT 0"
            },
            {
                "table": "paper_check_results",
                "column": "major_problems", 
                "definition": "INTEGER DEFAULT 0"
            },
            {
                "table": "paper_check_results",
                "column": "minor_problems",
                "definition": "INTEGER DEFAULT 0"
            },
            {
                "table": "paper_check_results",
                "column": "detailed_results",
                "definition": "JSONB DEFAULT '{}'"
            },
            
            # problems表缺失的列
            {
                "table": "problems",
                "column": "document_id",
                "definition": "VARCHAR(50)"
            },
            {
                "table": "problems",
                "column": "category",
                "definition": "VARCHAR(50) DEFAULT 'format'"
            },
            {
                "table": "problems",
                "column": "title",
                "definition": "VARCHAR(200) DEFAULT ''"
            },
            {
                "table": "problems",
                "column": "range_start",
                "definition": "INTEGER"
            },
            {
                "table": "problems",
                "column": "range_end",
                "definition": "INTEGER"
            },
            {
                "table": "problems",
                "column": "page_number",
                "definition": "INTEGER"
            },
            {
                "table": "problems",
                "column": "auto_fixable",
                "definition": "BOOLEAN DEFAULT FALSE"
            },
        ]
        
        for addition in column_additions:
            try:
                # 检查列是否已存在
                result = await self.session.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_schema = 'public' 
                        AND table_name = :table_name 
                        AND column_name = :column_name
                    )
                """), {
                    "table_name": addition["table"],
                    "column_name": addition["column"]
                })
                
                if not result.scalar():
                    await self.session.execute(text(f"""
                        ALTER TABLE {addition["table"]} 
                        ADD COLUMN {addition["column"]} {addition["definition"]}
                    """))
                    logger.info(f"已添加列: {addition['table']}.{addition['column']}")
                    self.migration_log.append(f"ADD_COLUMN: {addition['table']}.{addition['column']}")
                else:
                    logger.info(f"列已存在: {addition['table']}.{addition['column']}")
                    
            except Exception as e:
                logger.error(f"添加列失败: {addition['table']}.{addition['column']} - {e}")
    
    async def update_data_types(self):
        """更新数据类型"""
        logger.info("开始更新数据类型...")
        
        type_updates = [
            # 将email字段长度扩展到320（RFC标准）
            {
                "table": "users",
                "column": "email",
                "old_type": "VARCHAR(255)",
                "new_type": "VARCHAR(320)"
            },
            # 确保JSON字段使用JSONB
            {
                "table": "tasks",
                "column": "analysis_options",
                "old_type": "JSON",
                "new_type": "JSONB"
            },
            {
                "table": "tasks",
                "column": "result",
                "old_type": "JSON", 
                "new_type": "JSONB"
            },
        ]
        
        for update in type_updates:
            try:
                await self.session.execute(text(f"""
                    ALTER TABLE {update["table"]} 
                    ALTER COLUMN {update["column"]} TYPE {update["new_type"]} 
                    USING {update["column"]}::{update["new_type"]}
                """))
                logger.info(f"已更新数据类型: {update['table']}.{update['column']} -> {update['new_type']}")
                self.migration_log.append(f"UPDATE_TYPE: {update['table']}.{update['column']} -> {update['new_type']}")
            except Exception as e:
                logger.warning(f"更新数据类型失败: {update['table']}.{update['column']} - {e}")
    
    async def add_constraints(self):
        """添加约束"""
        logger.info("开始添加约束...")
        
        constraints = [
            # 用户名格式约束
            {
                "table": "users",
                "name": "username_format",
                "definition": "CHECK (username ~ '^[a-zA-Z0-9_-]{3,50}$')"
            },
            # 邮箱格式约束
            {
                "table": "users", 
                "name": "email_format",
                "definition": "CHECK (email ~ '^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$')"
            },
            # 文件大小约束
            {
                "table": "tasks",
                "name": "file_size_limit",
                "definition": "CHECK (file_size > 0 AND file_size <= 52428800)"
            },
            # 任务状态约束
            {
                "table": "tasks",
                "name": "valid_status",
                "definition": "CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled'))"
            },
            # 任务类型约束
            {
                "table": "tasks",
                "name": "valid_task_type", 
                "definition": "CHECK (task_type IN ('paper_check', 'content_analysis', 'format_check', 'document_parse'))"
            },
            # 论文检测结果约束
            {
                "table": "paper_check_results",
                "name": "valid_compliance_status",
                "definition": "CHECK (compliance_status IN ('compliant', 'partially_compliant', 'non_compliant', 'unknown'))"
            },
            # 问题严重程度约束
            {
                "table": "problems",
                "name": "valid_severity",
                "definition": "CHECK (severity IN ('severe', 'general', 'suggestion'))"
            },
            # 问题类别约束
            {
                "table": "problems",
                "name": "valid_category",
                "definition": "CHECK (category IN ('format', 'structure', 'citation', 'reference', 'style', 'content', 'layout', 'typography'))"
            },
        ]
        
        for constraint in constraints:
            try:
                await self.session.execute(text(f"""
                    ALTER TABLE {constraint["table"]} 
                    ADD CONSTRAINT {constraint["name"]} {constraint["definition"]}
                """))
                logger.info(f"已添加约束: {constraint['table']}.{constraint['name']}")
                self.migration_log.append(f"ADD_CONSTRAINT: {constraint['table']}.{constraint['name']}")
            except Exception as e:
                logger.warning(f"添加约束失败: {constraint['table']}.{constraint['name']} - {e}")
    
    async def create_optimized_indexes(self):
        """创建优化后的索引"""
        logger.info("开始创建优化索引...")
        
        indexes = [
            # 复合索引优化查询性能
            "CREATE INDEX IF NOT EXISTS idx_tasks_user_status ON tasks (user_id, status)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_status_created ON tasks (status, created_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_problems_result_severity ON problems (result_id, severity)",
            "CREATE INDEX IF NOT EXISTS idx_problems_doc_category ON problems (document_id, category)",
            "CREATE INDEX IF NOT EXISTS idx_orders_user_status ON orders (user_id, status)",
            
            # 条件索引优化特定查询
            "CREATE INDEX IF NOT EXISTS idx_users_active ON users (is_active) WHERE is_active = TRUE",
            "CREATE INDEX IF NOT EXISTS idx_tasks_completed ON tasks (completed_at DESC) WHERE status = 'completed'",
            "CREATE INDEX IF NOT EXISTS idx_problems_auto_fixable ON problems (auto_fixable) WHERE auto_fixable = TRUE",
            "CREATE INDEX IF NOT EXISTS idx_orders_paid ON orders (paid_at DESC) WHERE status = 'paid'",
            
            # JSONB字段的GIN索引
            "CREATE INDEX IF NOT EXISTS idx_tasks_analysis_options_gin ON tasks USING GIN (analysis_options)",
            "CREATE INDEX IF NOT EXISTS idx_tasks_result_gin ON tasks USING GIN (result)",
            "CREATE INDEX IF NOT EXISTS idx_paper_check_results_detailed_gin ON paper_check_results USING GIN (detailed_results)",
        ]
        
        for index_sql in indexes:
            try:
                await self.session.execute(text(index_sql))
                index_name = index_sql.split("idx_")[1].split(" ")[0] if "idx_" in index_sql else "unknown"
                logger.info(f"已创建索引: idx_{index_name}")
                self.migration_log.append(f"CREATE_INDEX: idx_{index_name}")
            except Exception as e:
                logger.warning(f"创建索引失败: {index_sql} - {e}")
    
    async def update_foreign_keys(self):
        """更新外键关系"""
        logger.info("开始更新外键关系...")
        
        # 为problems表添加document_id外键（如果列已存在但外键不存在）
        try:
            await self.session.execute(text("""
                ALTER TABLE problems 
                ADD CONSTRAINT fk_problems_document_id 
                FOREIGN KEY (document_id) REFERENCES documents(document_id) ON DELETE CASCADE
            """))
            logger.info("已添加外键: problems.document_id -> documents.document_id")
            self.migration_log.append("ADD_FK: problems.document_id -> documents.document_id")
        except Exception as e:
            logger.warning(f"添加外键失败: {e}")
    
    async def populate_missing_data(self):
        """填充缺失的数据"""
        logger.info("开始填充缺失数据...")
        
        try:
            # 为problems表填充document_id（从result_id关联获取）
            await self.session.execute(text("""
                UPDATE problems 
                SET document_id = pcr.document_id
                FROM paper_check_results pcr
                WHERE problems.result_id = pcr.result_id 
                AND problems.document_id IS NULL
            """))
            
            # 更新问题统计数据
            await self.session.execute(text("""
                UPDATE paper_check_results 
                SET 
                    total_problems = (
                        SELECT COUNT(*) FROM problems WHERE result_id = paper_check_results.result_id
                    ),
                    major_problems = (
                        SELECT COUNT(*) FROM problems 
                        WHERE result_id = paper_check_results.result_id AND severity = 'severe'
                    ),
                    minor_problems = (
                        SELECT COUNT(*) FROM problems 
                        WHERE result_id = paper_check_results.result_id AND severity IN ('general', 'suggestion')
                    )
                WHERE total_problems = 0 OR total_problems IS NULL
            """))
            
            logger.info("缺失数据填充完成")
            self.migration_log.append("POPULATE_DATA: problems.document_id and statistics")
            
        except Exception as e:
            logger.warning(f"填充数据失败: {e}")
    
    async def create_triggers(self):
        """创建触发器"""
        logger.info("开始创建触发器...")
        
        # 创建自动更新问题统计的触发器函数
        trigger_function = """
        CREATE OR REPLACE FUNCTION update_problem_counts()
        RETURNS TRIGGER AS $$
        BEGIN
            IF TG_OP = 'INSERT' THEN
                UPDATE paper_check_results 
                SET 
                    total_problems = total_problems + 1,
                    major_problems = CASE WHEN NEW.severity = 'severe' THEN major_problems + 1 ELSE major_problems END,
                    minor_problems = CASE WHEN NEW.severity IN ('general', 'suggestion') THEN minor_problems + 1 ELSE minor_problems END
                WHERE result_id = NEW.result_id;
                RETURN NEW;
            ELSIF TG_OP = 'DELETE' THEN
                UPDATE paper_check_results 
                SET 
                    total_problems = total_problems - 1,
                    major_problems = CASE WHEN OLD.severity = 'severe' THEN major_problems - 1 ELSE major_problems END,
                    minor_problems = CASE WHEN OLD.severity IN ('general', 'suggestion') THEN minor_problems - 1 ELSE minor_problems END
                WHERE result_id = OLD.result_id;
                RETURN OLD;
            END IF;
            RETURN NULL;
        END;
        $$ language 'plpgsql';
        """
        
        try:
            await self.session.execute(text(trigger_function))
            
            # 创建触发器
            await self.session.execute(text("""
                DROP TRIGGER IF EXISTS problems_count_trigger ON problems;
                CREATE TRIGGER problems_count_trigger
                    AFTER INSERT OR DELETE ON problems
                    FOR EACH ROW
                    EXECUTE FUNCTION update_problem_counts();
            """))
            
            logger.info("触发器创建完成")
            self.migration_log.append("CREATE_TRIGGER: problems_count_trigger")
            
        except Exception as e:
            logger.warning(f"创建触发器失败: {e}")
    
    async def run_migration(self):
        """执行完整的迁移过程"""
        logger.info("开始数据库优化迁移...")
        
        try:
            # 1. 备份现有数据
            await self.backup_tables()
            
            # 2. 添加缺失的列
            await self.add_missing_columns()
            
            # 3. 更新数据类型
            await self.update_data_types()
            
            # 4. 填充缺失数据
            await self.populate_missing_data()
            
            # 5. 添加约束
            await self.add_constraints()
            
            # 6. 更新外键关系
            await self.update_foreign_keys()
            
            # 7. 创建优化索引
            await self.create_optimized_indexes()
            
            # 8. 创建触发器
            await self.create_triggers()
            
            # 提交所有更改
            await self.session.commit()
            
            logger.info("数据库优化迁移完成!")
            logger.info("迁移日志:")
            for log_entry in self.migration_log:
                logger.info(f"  - {log_entry}")
                
        except Exception as e:
            await self.session.rollback()
            logger.error(f"迁移失败，已回滚: {e}")
            raise

async def run_migration():
    """运行迁移脚本的主函数"""
    async for session in get_db_session():
        migration = DatabaseMigration(session)
        await migration.run_migration()
        break

if __name__ == "__main__":
    asyncio.run(run_migration()) 