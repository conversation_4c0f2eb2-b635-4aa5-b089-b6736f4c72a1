<template>
  <BaseLayout 
    title="上传文档" 
    description="上传您的Word文档，选择分析类型，开始智能检测"
  >
        <!-- 上传区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- 上传和配置区域 -->
          <div class="lg:col-span-2">
            <!-- 文件上传区域 -->
        <BaseCard class="mb-6">
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">选择文档</h3>
          </template>
          
          <!-- 余额不足提示 -->
          <div v-if="uploadError === 'INSUFFICIENT_BALANCE'" class="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-500/30 rounded-lg">
            <div class="flex items-start">
              <svg class="h-5 w-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zm-2 4a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
              </svg>
              <div>
                <h4 class="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-1">检测次数不足</h4>
                <p class="text-sm text-yellow-700 dark:text-yellow-300 mb-3">您的账户余额已用尽，请充值后继续使用。</p>
                <BaseButton @click="goToPricing" variant="primary" size="sm">立即充值</BaseButton>
              </div>
            </div>
          </div>
                
                <!-- 错误提示区域 -->
                <div v-if="uploadError && uploadError !== 'INSUFFICIENT_BALANCE' && !showUploadModal" class="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-500/30 rounded-lg">
                  <div class="flex items-start">
                    <svg class="h-5 w-5 text-red-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <div class="flex-1">
                      <h4 class="text-sm font-semibold text-red-800 dark:text-red-200 mb-1">文件选择错误</h4>
                      <p class="text-sm text-red-700 dark:text-red-300 mb-3">{{ uploadError }}</p>
                      
                      <!-- 余额不足时显示充值按钮 -->
                      <div v-if="uploadError.includes('检测次数不足') || uploadError.includes('402') || uploadError.includes('余额') || uploadError.includes('充值')" class="mt-3">
                        <BaseButton @click="goToPricing" variant="primary" size="sm">
                          立即充值
                        </BaseButton>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 拖拽上传区域 -->
                <div 
                  v-show="selectedFiles.length === 0"
                  @drop="handleDrop"
                  @dragover="handleDragOver"
                  @dragenter="handleDragEnter"
                  @dragleave="handleDragLeave"
                  :class="['upload-zone mb-4', { 'active': isDragOver }]"
                >
                  <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                    </svg>
                    <p class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      拖拽文件到此处或点击选择
                    </p>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      支持 .doc 和 .docx 格式，单个文件最大 10MB
                    </p>
                    <input 
                      ref="fileInput"
                      type="file" 
                      accept=".doc,.docx" 
                      class="hidden"
                      @change="handleFileSelect"
                    >
              <BaseButton variant="primary" @click="openFileDialog">
                      选择文件
              </BaseButton>
                  </div>
                </div>
                
                <!-- 文件列表 -->
                <div v-show="selectedFiles.length > 0" class="space-y-3">
                  <div 
                    v-for="(file, index) in selectedFiles" 
                    :key="index"
                    class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <div class="flex items-center space-x-3">
                      <div :class="['file-icon', getFileExtension(file.name)]">
                        {{ getFileExtension(file.name).toUpperCase() }}
                      </div>
                      <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ file.name }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ formatFileSize(file.size) }}</p>
                      </div>
                    </div>
              <BaseButton 
                @click="clearSelectedFile" 
                variant="danger" 
                size="sm"
                prepend-icon="M6 18L18 6M6 6l12 12"
              >
                移除
              </BaseButton>
            </div>
          </div>
        </BaseCard>

            <!-- 检测标准选择 -->
        <BaseCard class="mb-6">
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">检测标准</h3>
          </template>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <!-- GB/T 7713.1-2006 -->
                  <label class="cursor-pointer h-full">
                    <input 
                      type="radio" 
                      name="detection-standard" 
                      value="gbt_7713_1_2006" 
                      v-model="detectionStandard"
                      class="sr-only peer"
                    >
                    <div class="border-2 border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center transition-all peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20 hover:border-gray-300 dark:hover:border-gray-500 h-full flex flex-col">
                      <div class="mx-auto h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
                        <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                      </div>
                      <h4 class="font-semibold text-gray-900 dark:text-white mb-2">GB/T 7713.1-2006</h4>
                      <p class="text-sm text-gray-600 dark:text-gray-400 flex-1">学位论文编写规则</p>
                      <p class="text-xs text-blue-600 dark:text-blue-400 mt-2">结构检查 + 格式检查</p>
                    </div>
                  </label>
                  
                  <!-- GB/T 7714-2015 -->
                  <label class="cursor-pointer h-full">
                    <input 
                      type="radio" 
                      name="detection-standard" 
                      value="gbt_7714_2015" 
                      v-model="detectionStandard"
                      class="sr-only peer"
                    >
                    <div class="border-2 border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center transition-all peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20 hover:border-gray-300 dark:hover:border-gray-500 h-full flex flex-col">
                      <div class="mx-auto h-10 w-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-3">
                        <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                      </div>
                      <h4 class="font-semibold text-gray-900 dark:text-white mb-2">GB/T 7714-2015</h4>
                      <p class="text-sm text-gray-600 dark:text-gray-400 flex-1">参考文献著录规则</p>
                      <p class="text-xs text-green-600 dark:text-green-400 mt-2">引用格式检查</p>
                    </div>
                  </label>
                  
                  <!-- 河北科技学院本科论文检查 -->
                  <label class="cursor-pointer h-full">
                    <input 
                      type="radio" 
                      name="detection-standard" 
                      value="hbkj_bachelor_2024" 
                      v-model="detectionStandard"
                      class="sr-only peer"
                    >
                    <div class="border-2 border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center transition-all peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20 hover:border-gray-300 dark:hover:border-gray-500 h-full flex flex-col">
                      <div class="mx-auto h-10 w-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mb-3">
                        <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                      </div>
                      <h4 class="font-semibold text-gray-900 dark:text-white mb-2">河北科技学院</h4>
                      <p class="text-sm text-gray-600 dark:text-gray-400 flex-1">本科论文检查</p>
                      <p class="text-xs text-purple-600 dark:text-purple-400 mt-2">全面检查 + 任务书</p>
                    </div>
                  </label>
                </div>
        </BaseCard>

            <!-- 开始分析按钮 -->
        <div class="text-center">
          <!-- 余额不足时的特殊提示 -->
          <div v-if="(userStore.currentUser?.check_balance ?? 0) === 0" class="mb-4 p-4 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-500/30 rounded-lg">
            <div class="flex items-center justify-center">
              <svg class="h-5 w-5 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <p class="text-sm font-medium text-orange-800 dark:text-orange-200 mr-4">
                检测次数已用完，请充值后继续使用
              </p>
              <BaseButton @click="goToPricing" variant="primary" size="sm">
                立即充值
              </BaseButton>
            </div>
          </div>
          
          <BaseButton 
            @click="startAnalysis"
            variant="primary" 
            size="lg"
            :disabled="selectedFiles.length === 0 || isUploading || (userStore.currentUser?.check_balance ?? 0) === 0"
            :loading="isUploading"
            prepend-icon="M13 10V3L4 14h7v7l9-11h-7z"
          >
            {{ isUploading ? '正在上传...' : (userStore.currentUser?.check_balance ?? 0) === 0 ? '余额不足' : '开始分析' }}
          </BaseButton>
        </div>
      </div>
      
      <!-- 右侧信息栏 -->
      <div class="lg:col-span-1">
        <!-- 分析类型说明 -->
        <BaseCard class="mb-6">
          <template #header>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">{{ detectionStandardLabel }}</h4>
          </template>
          
          <div v-if="detectionStandard === 'gbt_7713_1_2006'">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
              基于《学位论文编写规则》国家标准的全面检测，包含以下功能：
            </p>
            <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                封面信息完整性检查
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                标题层次格式规范验证
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                章节结构组织检查
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                字体样式和段落格式检查
              </li>
            </ul>
          </div>
          
          <div v-else-if="detectionStandard === 'gbt_7714_2015'">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
              基于《参考文献著录规则》国家标准的专业检测，包含以下功能：
            </p>
            <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                专著[M]著录格式检查
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                期刊[J]著录格式验证
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                引用编号序列检查
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                文献类型标识符验证
              </li>
            </ul>
          </div>
          
          <div v-else-if="detectionStandard === 'hbkj_bachelor_2024'">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
              基于河北科技学院本科论文要求的全面检测，包含以下功能：
            </p>
            <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                封面信息完整性检查
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                毕业设计任务书检查
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                开题报告格式验证
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                诚信声明和版权声明检查
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                标题格式和页面设置检查
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                章节顺序和结构完整性检查
              </li>
            </ul>
          </div>

          <div v-else>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              请选择分析类型以查看详细说明
            </p>
          </div>
        </BaseCard>

            <!-- 使用说明 -->
        <BaseCard class="mb-6">
          <template #header>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">使用说明</h4>
          </template>
          
                <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li class="flex items-start">
                    <span class="text-blue-600 dark:text-blue-400 mr-2">1.</span>
                    选择或拖拽一个Word文档到上传区域
                  </li>
                  <li class="flex items-start">
                    <span class="text-blue-600 dark:text-blue-400 mr-2">2.</span>
                    选择合适的检测标准
                  </li>
                  <li class="flex items-start">
                    <span class="text-blue-600 dark:text-blue-400 mr-2">3.</span>
                    点击"开始分析"按钮
                  </li>
                  <li class="flex items-start">
                    <span class="text-blue-600 dark:text-blue-400 mr-2">4.</span>
                    等待分析完成，查看详细报告
                  </li>
                </ul>
        </BaseCard>

            <!-- 支持格式 -->
        <BaseCard class="mb-6">
          <template #header>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">支持格式</h4>
          </template>
          
                <div class="space-y-3">
                  <div class="flex items-center">
              <div class="file-icon docx mr-3">DOCX</div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">Microsoft Word 2007+</p>
                      <p class="text-sm text-gray-600 dark:text-gray-400">.docx 格式</p>
                    </div>
                  </div>
                </div>
        </BaseCard>

            <!-- 剩余次数 -->
        <BaseCard>
          <template #header>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">剩余次数</h4>
          </template>
          
                <div class="text-center">
                  <div :class="[
                    'text-3xl font-bold mb-2',
                    (userStore.currentUser?.check_balance ?? 0) === 0 
                      ? 'text-red-600 dark:text-red-400' 
                      : (userStore.currentUser?.check_balance ?? 0) <= 3
                        ? 'text-orange-600 dark:text-orange-400'
                        : 'text-blue-600 dark:text-blue-400'
                  ]">
                    {{ userStore.currentUser?.check_balance ?? 0 }}
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">本月剩余检测次数</p>
                  
                  <!-- 余额状态提示 -->
                  <div v-if="(userStore.currentUser?.check_balance ?? 0) === 0" class="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-500/30 rounded-lg">
                    <div class="flex items-center justify-center">
                      <svg class="h-4 w-4 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <p class="text-xs text-red-700 dark:text-red-300 font-medium">余额已用完</p>
                    </div>
                  </div>
                  <div v-else-if="(userStore.currentUser?.check_balance ?? 0) <= 3" class="mb-4 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-500/30 rounded-lg">
                    <div class="flex items-center justify-center">
                      <svg class="h-4 w-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <p class="text-xs text-orange-700 dark:text-orange-300 font-medium">余额不足，建议充值</p>
                    </div>
                  </div>
                  
            <BaseButton 
              :variant="(userStore.currentUser?.check_balance ?? 0) === 0 ? 'primary' : 'secondary'"
              size="sm" 
              @click="goToPricing" 
              class="w-full"
            >
              {{ (userStore.currentUser?.check_balance ?? 0) === 0 ? '立即充值' : '购买更多次数' }}
            </BaseButton>
                </div>
        </BaseCard>
          </div>
        </div>

        <!-- 上传进度模态框 -->
    <div v-show="showUploadModal" class="fixed inset-0 bg-gray-900/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-lg mx-auto transform transition-all">
        
        <!-- 头部图标区域 -->
        <div class="flex items-center justify-center pt-6 pb-4">
          <div class="flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full">
            <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </div>
          
          <!-- 关闭按钮 -->
          <button 
            @click="cancelAnalysis" 
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
        
        <!-- 标题 -->
        <div class="text-center px-6 pb-6">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {{ uploadError ? '处理失败' : isTaskCompleted ? '分析完成' : '文档分析中' }}
          </h3>
          <p class="text-gray-600 dark:text-gray-300 text-sm">
            {{ uploadError ? '文档处理过程中发生错误' : isTaskCompleted ? '文档分析已成功完成' : '正在智能分析您的文档，请耐心等待' }}
          </p>
        </div>
        
        <!-- 内容区域 -->
        <div class="px-6 pb-6 space-y-6">
          <!-- 当前处理的文件 -->
          <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
            <div class="flex items-center space-x-4">
              <div class="file-icon docx flex-shrink-0">
                {{ getFileExtension(currentFileName) === 'docx' ? 'DOCX' : 'DOC' }}
              </div>
              <div class="flex-1 min-w-0">
                <p class="font-medium text-gray-900 dark:text-white truncate" :title="currentFileName">
                  {{ currentFileName || selectedFiles[0]?.name || '未知文件' }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ detectionStandardLabel }}</p>
              </div>
            </div>
          </div>
          
          <!-- 进度区域 -->
          <div v-if="!uploadError">
            <div class="flex justify-between items-center mb-3">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">分析进度</span>
              <span class="text-xl font-bold text-blue-600 dark:text-blue-400">{{ Math.round(progress) }}%</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden">
              <div 
                class="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500 h-2.5 rounded-full transition-all duration-500 ease-out"
                :style="{ width: progress + '%' }"
              ></div>
            </div>
          </div>
          
          <!-- 错误信息显示 -->
          <div v-if="uploadError" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-500/30 rounded-lg p-4">
            <div class="flex items-start">
              <svg class="h-5 w-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <div class="flex-1">
                <h4 class="text-sm font-semibold text-red-800 dark:text-red-200 mb-1">错误详情</h4>
                <p class="text-sm text-red-700 dark:text-red-300">{{ uploadError }}</p>
              </div>
            </div>
          </div>
          
          <!-- 成功信息显示 -->
          <div v-if="isTaskCompleted && !uploadError" class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-500/30 rounded-lg p-4">
            <div class="flex items-start">
              <svg class="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              <div class="flex-1">
                <h4 class="text-sm font-semibold text-green-800 dark:text-green-200 mb-1">分析完成</h4>
                <p class="text-sm text-green-700 dark:text-green-300">文档已成功分析，您可以查看详细报告</p>
              </div>
            </div>
          </div>
          
          <!-- 处理步骤 -->
          <div v-if="!uploadError" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
              <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
              </svg>
              处理步骤
            </h4>
            <div class="space-y-3">
              <div v-for="(step, index) in analysisSteps" :key="index" class="flex items-center">
                <div class="flex-shrink-0 mr-3">
                  <!-- 完成状态 -->
                  <div v-if="step.status === 'completed'" class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center ring-2 ring-green-200 dark:ring-green-800">
                    <svg class="h-4 w-4 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                  </div>
                  <!-- 处理中状态 -->
                  <div v-else-if="step.status === 'processing'" class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center ring-2 ring-blue-200 dark:ring-blue-800">
                    <svg class="h-4 w-4 text-blue-600 dark:text-blue-400 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                  </div>
                  <!-- 失败状态 -->
                  <div v-else-if="step.status === 'failed'" class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center ring-2 ring-red-200 dark:ring-red-800">
                    <svg class="h-4 w-4 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                  </div>
                  <!-- 等待状态 -->
                  <div v-else class="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center ring-2 ring-gray-200 dark:ring-gray-600">
                    <div class="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full"></div>
                  </div>
                </div>
                <div class="flex-1">
                  <p :class="[
                    'text-sm font-medium',
                    step.status === 'completed' ? 'text-green-700 dark:text-green-300' : 
                    step.status === 'processing' ? 'text-blue-700 dark:text-blue-300' : 
                    step.status === 'failed' ? 'text-red-700 dark:text-red-300' : 
                    'text-gray-500 dark:text-gray-400'
                  ]">
                    {{ step.name }}
                  </p>
                  <p v-if="step.status === 'processing'" class="text-xs text-blue-600 dark:text-blue-400 mt-0.5">
                    正在处理中...
                  </p>
                  <p v-else-if="step.status === 'completed'" class="text-xs text-green-600 dark:text-green-400 mt-0.5">
                    已完成
                  </p>
                  <p v-else-if="step.status === 'failed'" class="text-xs text-red-600 dark:text-red-400 mt-0.5">
                    处理失败
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 底部按钮区域 -->
        <div class="border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50 rounded-b-xl px-6 py-4">
          <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 space-y-reverse sm:space-y-0">
            <!-- 取消/关闭按钮 -->
            <BaseButton 
              v-if="!isTaskCompleted || uploadError" 
              variant="secondary" 
              @click="cancelAnalysis" 
              :disabled="isUploading"
              class="w-full sm:w-auto px-4 py-2.5 font-medium"
            >
              {{ uploadError ? '关闭' : '取消分析' }}
            </BaseButton>
            
            <!-- 查看结果按钮 -->
            <BaseButton 
              v-if="isTaskCompleted && !uploadError" 
              variant="primary" 
              @click="() => { showUploadModal = false; router.push('/documents'); }"
              class="w-full sm:w-auto px-4 py-2.5 font-medium"
              prepend-icon="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            >
              查看结果
            </BaseButton>
            
            <!-- 重新尝试按钮 -->
            <BaseButton 
              v-if="uploadError" 
              variant="primary" 
              @click="retryAnalysis"
              class="w-full sm:w-auto px-4 py-2.5 font-medium"
              prepend-icon="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            >
              重新尝试
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseLayout from '../components/BaseLayout.vue'
import BaseCard from '../components/BaseCard.vue'
import BaseButton from '../components/BaseButton.vue'
import { DocumentApi } from '@/services/documentApi'
import { TaskApi } from '@/services/taskApi'
import type { DocumentUploadOptions } from '@/services/documentApi'
import type { Task as TaskProgress } from '@/types/index'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const documentApi = new DocumentApi()
const taskApi = new TaskApi()
const userStore = useUserStore()

// 响应式数据
const selectedFiles = ref<File[]>([])
const detectionStandard = ref<'gbt_7713_1_2006' | 'gbt_7714_2015' | 'hbkj_bachelor_2024'>('gbt_7713_1_2006')
const isDragOver = ref(false)
const showUploadModal = ref(false)
const progress = ref(0)
const currentFileName = ref('')
const fileInput = ref<HTMLInputElement>()
const isUploading = ref(false)
const currentTaskId = ref<string>('')
const uploadError = ref<string>('')
const taskPoller = ref<{ cancel: () => void } | null>(null)

// 文件校验常量
const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ALLOWED_FILE_TYPES = ['.doc', '.docx']

// 分析步骤
const analysisSteps = ref([
  { name: '文档上传', status: 'pending' },
  { name: '内容提取', status: 'pending' },
  { name: '格式检测', status: 'pending' },
  { name: '生成报告', status: 'pending' }
])

// 计算属性
const detectionStandardLabel = computed(() => {
  switch (detectionStandard.value) {
    case 'gbt_7713_1_2006':
      return 'GB/T 7713.1-2006 学位论文编写规则'
    case 'gbt_7714_2015':
      return 'GB/T 7714-2015 参考文献著录规则'
    case 'hbkj_bachelor_2024':
      return '河北科技学院本科论文检查'
    default:
      return '论文检测'
  }
})

const isTaskCompleted = computed(() => {
  return analysisSteps.value.every(step => step.status === 'completed')
})

// 文件校验函数
const validateFile = (file: File): string | null => {
  // 检查文件类型
  const fileName = file.name.toLowerCase()
  const isValidType = ALLOWED_FILE_TYPES.some(type => fileName.endsWith(type))
  if (!isValidType) {
    return `不支持的文件格式。请选择 ${ALLOWED_FILE_TYPES.join(' 或 ')} 格式的文件。`
  }
  
  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    const maxSizeMB = MAX_FILE_SIZE / 1024 / 1024
    const fileSizeMB = (file.size / 1024 / 1024).toFixed(2)
    return `文件太大。${file.name} (${fileSizeMB}MB) 超过了 ${maxSizeMB}MB 的限制。`
  }
  
  return null
}

// 方法
const openFileDialog = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    
    // 校验文件
    const error = validateFile(file)
    if (error) {
      uploadError.value = error
      return
    }
    
    // 清除之前的错误，替换当前文件
    uploadError.value = ''
    selectedFiles.value = [file]
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
    const file = event.dataTransfer.files[0]
    
    // 校验文件
    const error = validateFile(file)
    if (error) {
      uploadError.value = error
      return
    }
    
    // 清除之前的错误，替换当前文件
    uploadError.value = ''
    selectedFiles.value = [file]
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const clearSelectedFile = () => {
  selectedFiles.value = []
  uploadError.value = ''
  // 重置文件输入框
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const getFileExtension = (filename: string) => {
  const ext = filename.split('.').pop()?.toLowerCase()
  return ext === 'docx' ? 'docx' : 'doc'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const resetAnalysisState = () => {
  progress.value = 0
  uploadError.value = ''
  currentTaskId.value = ''
  analysisSteps.value.forEach(step => {
    step.status = 'pending'
  })
  if (taskPoller.value) {
    taskPoller.value.cancel()
    taskPoller.value = null
  }
}

const updateStepsFromTaskProgress = (taskProgress: any) => {
  // 根据任务进度更新步骤状态
  // 处理后端返回的数据格式适配
  const status = taskProgress.status?.toLowerCase?.() || taskProgress.status
  let progressValue = taskProgress.progress || 0
  
  // 🔧 修复：如果任务已完成但进度为0，设置进度为100
  if (status === 'completed' && progressValue === 0) {
    progressValue = 100
  }
  
  progress.value = progressValue
  
  console.log('更新任务进度:', { status, progressValue, taskProgress })
  
  // 🔥 新增：优先使用后端返回的实际步骤状态
  if (taskProgress.live_progress && taskProgress.steps) {
    // 使用实时步骤状态数据
    const backendSteps = taskProgress.steps
    
    // 更新每个步骤的状态
    Object.keys(backendSteps).forEach((stepId, index) => {
      if (index < analysisSteps.value.length) {
        const backendStep = backendSteps[stepId]
        const frontendStep = analysisSteps.value[index]
        
        // 映射后端状态到前端状态
        let frontendStatus = 'pending'
        if (backendStep.status === 'completed') {
          frontendStatus = 'completed'
        } else if (backendStep.status === 'running') {
          frontendStatus = 'processing'
        } else if (backendStep.status === 'failed') {
          frontendStatus = 'failed'
        }
        
        frontendStep.status = frontendStatus
        
        // 更新步骤名称（如果后端提供了）
        if (backendStep.name && backendStep.name !== frontendStep.name) {
          frontendStep.name = backendStep.name
        }
      }
    })
    
    console.log('✅ 使用实时步骤状态更新UI')
  } else {
    // 回退到基于进度百分比的状态更新（兼容性）
    console.log('🔄 使用百分比进度更新UI（回退模式）')
    
    // 重置所有步骤状态
    analysisSteps.value.forEach(step => {
      step.status = 'pending'
    })
    
    if (status === 'pending') {
      // 任务等待中，只有第一步完成（文档上传）
      if (progressValue >= 10) {
        analysisSteps.value[0].status = 'completed'
      }
      if (progressValue === 0) {
        progress.value = 10 // 至少显示10%，表示文档已上传
      }
    } else if (status === 'processing' || status === 'running') {
      // 任务运行中，根据进度更新步骤
      
      if (progressValue >= 25) {
        analysisSteps.value[0].status = 'completed' // 文档上传验证
      }
      if (progressValue >= 50) {
        analysisSteps.value[1].status = 'completed' // 文档内容提取
      }
      if (progressValue >= 80) {
        analysisSteps.value[2].status = 'completed' // 格式规则检测
      }
      if (progressValue >= 100) {
        analysisSteps.value[3].status = 'completed' // 生成分析报告
      }
      
      // 设置当前步骤为处理中
      const currentStepIndex = Math.floor(progressValue / 25)
      if (currentStepIndex < analysisSteps.value.length && progressValue < 100) {
        analysisSteps.value[currentStepIndex].status = 'processing'
      }
    } else if (status === 'completed') {
      // 任务完成，所有步骤都完成
      analysisSteps.value.forEach((step, index) => {
        analysisSteps.value[index].status = 'completed'
      })
      progress.value = 100
    } else if (status === 'failed') {
      // 任务失败，标记失败的步骤
      const currentStepIndex = Math.floor(progressValue / 25)
      if (currentStepIndex < analysisSteps.value.length) {
        analysisSteps.value[currentStepIndex].status = 'failed'
      }
    }
  }
  
  // 🔥 强制触发Vue响应性更新
  analysisSteps.value = [...analysisSteps.value]
  
  // 处理任务错误信息
  if (status === 'failed') {
    uploadError.value = taskProgress.error_message || '分析过程中发生错误'
    console.log('❌ 任务失败:', uploadError.value)
  }
  
  console.log('当前步骤状态:', analysisSteps.value.map(s => ({ name: s.name, status: s.status })))
}

const startAnalysis = async () => {
  if (selectedFiles.value.length === 0 || isUploading.value) {
    return;
  }
  isUploading.value = true;
  uploadError.value = '';

  const fileToUpload = selectedFiles.value[0];
  currentFileName.value = fileToUpload.name; 

  try {
    // 统一使用 paper_check 作为分析类型，将检测标准通过 options 传递
    const uploadOptions: DocumentUploadOptions = {
      analysisType: 'paper_check', // 固定使用论文检测类型
      options: {
        detection_standard: detectionStandard.value, // 传递检测标准
        standard_name: detectionStandardLabel.value   // 传递标准名称（用于显示）
      }
    };

    const response = await documentApi.uploadDocument(fileToUpload, uploadOptions);
    
    // 假设上传后总是返回任务ID并需要轮询
    if (response.task_id) {
        showUploadModal.value = true
        currentTaskId.value = response.task_id
        startTaskPolling(response.task_id)
    } else {
        // 作为备用，如果直接完成，显示成功
        console.warn("任务直接完成，未返回 task_id:", response);
        // ... 此处可以添加直接完成的UI逻辑 ...
    }

  } catch (error: any) {
    console.error('Upload failed:', error);
    if (error.response && error.response.status === 402) {
      uploadError.value = 'INSUFFICIENT_BALANCE';
    } else {
      uploadError.value = error.response?.data?.detail || error.message || '上传或创建任务时发生未知错误，请重试。';
    }
  } finally {
      isUploading.value = false;
  }
};

const goToPricing = () => {
  router.push('/pricing');
}

const startTaskPolling = (taskId: string) => {
  console.log('开始轮询任务状态 (优化版本):', taskId);

  if (taskPoller.value) {
    taskPoller.value.cancel();
  }

  let cancelled = false;
  const timeoutId = setInterval(async () => {
    if (cancelled) {
      clearInterval(timeoutId);
      return;
    }

    try {
      // 🔥 优化：使用新的进度API端点获取详细进度信息
      const taskProgress = await taskApi.getTaskProgress(taskId);
      console.log('进度API返回数据:', taskProgress);
      
      // 更新UI状态
      updateStepsFromTaskProgress(taskProgress);

      const status = taskProgress.status?.toLowerCase?.() || taskProgress.status;
      if (status === 'completed' || status === 'failed') {
        console.log(`任务结束，状态: ${status}`);
        clearInterval(timeoutId);

        if (status === 'completed') {
          // 分析完成，弹窗保持打开状态，让用户手动选择操作
          console.log('分析完成，等待用户操作');
        } else {
          setTimeout(() => {
            showUploadModal.value = false;
          }, 3000);
        }
      }
    } catch (error) {
      console.error('轮询任务进度失败:', error);
      
      // 🔥 回退：如果进度API失败，尝试使用基础状态API
      try {
        const basicStatus = await taskApi.getTaskStatus(taskId);
        console.log('回退到基础状态API:', basicStatus);
        updateStepsFromTaskProgress(basicStatus);
        
        const status = basicStatus.status?.toLowerCase?.() || basicStatus.status;
        if (status === 'completed' || status === 'failed') {
          clearInterval(timeoutId);
        }
      } catch (fallbackError) {
        console.error('回退API也失败:', fallbackError);
        uploadError.value = '获取任务状态失败';
        clearInterval(timeoutId);
      }
    }
  }, 1500); // 🔥 优化：减少轮询间隔到1.5秒，提供更流畅的体验

  taskPoller.value = {
    cancel: () => {
      console.log('取消任务轮询');
      cancelled = true;
      clearInterval(timeoutId);
    }
  };

  console.log('任务轮询器已创建（优化版本）:', !!taskPoller.value);
}

const cancelAnalysis = () => {
  // 取消任务轮询
  if (taskPoller.value) {
    taskPoller.value.cancel()
    taskPoller.value = null
  }
  
  // 如果有任务ID，尝试取消任务（但只在特定状态下）
  if (currentTaskId.value) {
    // 检查当前任务状态，只有 pending 或 processing 状态才尝试取消
    const canCancel = analysisSteps.value.some(step => 
      step.status === 'processing' || 
      !analysisSteps.value.every(step => step.status === 'completed')
    )
    
    if (canCancel && !uploadError.value) {
      taskApi.cancelTask(currentTaskId.value)
        .then(() => {
          console.log('任务取消成功')
        })
        .catch(error => {
          console.warn('取消任务请求失败:', error)
          // 不显示错误给用户，因为任务可能已经完成
          // 这是正常情况，不需要用户关心
        })
    }
  }
  
  // 重置状态并关闭弹窗
  resetAnalysisState()
  showUploadModal.value = false
  isUploading.value = false
}

const retryAnalysis = () => {
  resetAnalysisState()
  uploadError.value = ''
  showUploadModal.value = false
  
  // 如果有选中的文件，重新开始分析
  if (selectedFiles.value.length > 0) {
    setTimeout(() => {
      startAnalysis()
    }, 100)
  }
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
})

// 组件卸载时清理资源
const cleanup = () => {
  if (taskPoller.value) {
    taskPoller.value.cancel()
    taskPoller.value = null
  }
}

// 监听页面卸载
window.addEventListener('beforeunload', cleanup)
</script>

<style scoped>
.upload-zone {
  @apply border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 transition-colors duration-200;
}

.upload-zone.active,
.upload-zone:hover {
  @apply border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/10;
}

.file-icon {
  @apply w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-semibold text-xs;
}

.file-icon.docx {
  @apply bg-blue-600;
}

.file-icon.doc {
  @apply bg-blue-700;
}

/* 移动端优化 */
@media (max-width: 640px) {
  .modal-container {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/* 优化的进度条动画 */
.progress-bar {
  transition: width 0.3s ease-in-out;
}

/* 弹窗入场动画 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-enter {
  animation: modalFadeIn 0.2s ease-out;
}
</style> 