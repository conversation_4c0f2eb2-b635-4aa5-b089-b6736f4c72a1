{"$schema": "http://json-schema.org/draft-07/schema#", "title": "论文检测规则标准定义", "description": "本文档定义了论文检测引擎规则文件的完整结构与所有配置项的说明。将此 schema 应用于您的规则文件，可以在编辑器中获得实时校验、智能提示和文档说明。", "type": "object", "properties": {"$schema": {"type": "string", "description": "用于关联此 schema 文件，以在编辑器中启用智能提示和校验。"}, "metadata": {"type": "object", "description": "描述规则文件的元数据信息。", "properties": {"standard_id": {"type": "string", "description": "标准的唯一ID，例如 'hbkj_bachelor_2024'。"}, "name": {"type": "string", "description": "标准的显示名称，例如 '河北科技学院学士学位论文检测标准'。"}, "version": {"type": "string", "description": "标准的版本号，例如 '2.0.0'。"}, "description": {"type": "string", "description": "对标准的简要描述。"}, "extends": {"type": ["string", "null"], "description": "（可选）继承另一个规则文件的相对路径。"}}, "required": ["standard_id", "name", "version"]}, "definitions": {"type": "object", "description": "定义可被复用的原子属性、样式组合和文档结构。", "properties": {"properties": {"type": "object", "description": "定义最基础、不可再分的原子化格式属性。", "patternProperties": {"^[a-zA-Z_]+$": {"type": "object", "patternProperties": {"^[a-zA-Z_]+$": {"type": "object", "properties": {"value": {"description": "属性的期望值。"}, "required": {"type": "boolean", "description": "此属性是否必须存在。"}, "errorMessage": {"type": "string", "description": "检查失败时返回的错误信息。"}, "severity": {"type": "string", "enum": ["info", "warning", "error"], "description": "问题的严重等级。"}, "alternatives": {"type": "array", "items": {"type": "string"}, "description": "可接受的备选值列表。"}, "unit": {"type": "string", "description": "值的单位（如 'cm', 'pt', '字符'）。"}, "tolerance": {"type": "number", "description": "数值比较时的容差范围。"}, "point_size": {"type": "number", "description": "字号对应的磅值。"}}}}}}}, "styles": {"type": "object", "description": "通过引用原子属性，组合成完整的、可复用的样式集。", "patternProperties": {"^[a-zA-Z_]+$": {"type": "object", "additionalProperties": {"type": "object"}}}}, "document_structure": {"type": "array", "description": "定义论文期望的章节结构、顺序和属性。", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "章节的显示名称。"}, "required": {"type": "boolean", "description": "该章节是否必须存在。"}, "identifiers": {"type": "array", "items": {"type": "string"}, "description": "用于在文档中识别此章节的关键字列表（大小写不敏感）。"}, "description": {"type": "string", "description": "对该章节的详细描述。"}, "format_rule": {"type": "object", "properties": {"$ref": {"type": "string"}}, "description": "链接到一条格式规则，用于校验此章节标题的格式。"}, "content_rules": {"type": "array", "items": {"type": "object", "properties": {"$ref": {"type": "string"}}}, "description": "链接到多条内容规则，用于校验此章节内容的属性。"}, "error_messages": {"type": "object", "properties": {"missing": {"type": "string"}, "out_of_order": {"type": "string"}}, "description": "结构检查失败时的特定错误信息。"}}, "required": ["name", "required", "identifiers"]}}}}, "rules": {"type": "object", "description": "定义所有可执行的检测规则，按类别组织。", "properties": {"structure": {"$ref": "#/definitions/rule_category"}, "content": {"$ref": "#/definitions/rule_category"}, "format": {"$ref": "#/definitions/rule_category"}}}, "execution_plan": {"type": "array", "description": "定义规则的执行阶段和顺序，引擎将严格按此计划执行。", "items": {"type": "object", "properties": {"phase": {"type": "string", "description": "阶段的唯一ID。"}, "description": {"type": "string", "description": "对该阶段的描述。"}, "rules": {"type": "array", "items": {"type": "object", "properties": {"$ref": {"type": "string"}}}}}, "required": ["phase", "rules"]}}}, "definitions": {"rule_category": {"type": "object", "description": "一个规则类别，包含多条具体的规则定义。", "patternProperties": {"^[a-zA-Z0-9_]+$": {"type": "object", "properties": {"name": {"type": "string", "description": "规则的显示名称。"}, "description": {"type": "string", "description": "对规则功能的详细描述。"}, "severity": {"type": "string", "enum": ["info", "warning", "error"], "description": "规则检查失败时的严重等级。"}, "check_function": {"type": "string", "description": "关联到引擎中实现的具体检查函数的名称。"}, "depends_on": {"type": "array", "items": {"type": "string"}, "description": "（可选）依赖的其他规则ID列表，用于控制执行顺序和短路。"}, "parameters": {"type": "object", "description": "传递给检查函数的参数对象。"}}, "required": ["name", "severity", "check_function", "parameters"]}}}}, "required": ["metadata", "definitions", "rules", "execution_plan"]}