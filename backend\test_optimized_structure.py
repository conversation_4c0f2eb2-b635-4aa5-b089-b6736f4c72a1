#!/usr/bin/env python3
"""
优化后的文档结构分析测试

测试目标：
1. 验证封面页检测功能
2. 验证基于字体大小和居中对齐的标题识别
3. 分析test.docx文档的真实结构
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_optimized_structure():
    """测试优化后的文档结构分析"""
    
    try:
        from app.services.document_processor import DocumentProcessor
        
        # 测试文档路径
        test_doc_path = project_root.parent / "docs" / "test.docx"
        
        if not test_doc_path.exists():
            print(f"❌ 测试文档不存在: {test_doc_path}")
            return False
        
        print(f"🔍 测试优化后的文档结构分析")
        print(f"📄 测试文档: {test_doc_path}")
        print("=" * 60)
        
        # 初始化处理器
        processor = DocumentProcessor()
        
        # 分析文档结构
        print("\n📊 开始分析文档结构...")
        structure = processor.analyze_document_structure(str(test_doc_path))
        
        # 1. 检查封面页检测结果
        print("\n📋 1. 封面页检测结果")
        print("-" * 30)
        
        cover_info = structure.get('cover_page', {})
        if cover_info.get('has_cover'):
            print("✅ 检测到封面页")
            print(f"   - 标题: {'✅' if cover_info.get('title_found') else '❌'}")
            print(f"   - 作者: {'✅' if cover_info.get('author_found') else '❌'}")
            print(f"   - 指导教师: {'✅' if cover_info.get('advisor_found') else '❌'}")
            print(f"   - 学校: {'✅' if cover_info.get('school_found') else '❌'}")
            print(f"   - 日期: {'✅' if cover_info.get('date_found') else '❌'}")
            
            print("\n   封面元素详情:")
            for element in cover_info.get('cover_elements', []):
                print(f"     {element['type']}: {element['text'][:50]}...")
        else:
            print("❌ 未检测到封面页")
        
        # 2. 检查标题识别结果
        print("\n🏗️ 2. 标题识别统计")
        print("-" * 30)
        
        outline = structure.get('outline', [])
        style_count = sum(1 for item in outline if item.get('type') == 'style')
        centered_count = sum(1 for item in outline if item.get('type') == 'centered')
        center_aligned_count = sum(1 for item in outline if item.get('alignment') == 'center')
        
        print(f"   - 总标题数: {len(outline)}")
        print(f"   - Word样式标题: {style_count}")
        print(f"   - 居中识别标题: {centered_count}")
        print(f"   - 居中对齐标题: {center_aligned_count}")
        
        # 3. 分析论文结构完整性
        print("\n📝 3. 论文结构完整性分析")
        print("-" * 30)
        
        # 检查关键结构
        structure_elements = {
            '封面': cover_info.get('has_cover', False),
            '声明': any('声明' in item.get('text', '') for item in outline),
            '授权书': any('授权' in item.get('text', '') for item in outline),
            '摘要': any('摘要' in item.get('text', '') for item in outline),
            '目录': any('目录' in item.get('text', '') for item in outline),
            '正文章节': any('第' in item.get('text', '') and '章' in item.get('text', '') for item in outline),
            '结论': any('结论' in item.get('text', '') for item in outline),
            '参考文献': any('参考文献' in item.get('text', '') for item in outline),
            '致谢': any('致谢' in item.get('text', '') for item in outline)
        }
        
        for element, found in structure_elements.items():
            status = "✅" if found else "❌"
            print(f"   {status} {element}")
        
        # 4. 显示前15个标题
        print("\n📋 4. 前15个识别的标题")
        print("-" * 30)
        
        for i, item in enumerate(outline[:15], 1):
            text = item.get('text', '')
            text_display = text[:40] + '...' if len(text) > 40 else text
            heading_type = item.get('type', 'unknown')
            alignment = item.get('alignment', 'unknown')
            level = item.get('level', 0)
            
            type_icon = {
                'style': '🎨',
                'centered': '📍',
                'unknown': '❓'
            }.get(heading_type, '❓')
            
            alignment_icon = {
                'center': '⬛',
                'left': '⬅️',
                'right': '➡️',
                'justify': '↔️'
            }.get(alignment, '❓')
            
            print(f"   {i:2d}. {type_icon} [{heading_type}] {alignment_icon} H{level} | {text_display}")
        
        # 5. 保存详细结果
        print("\n💾 5. 保存分析结果")
        print("-" * 30)
        
        output_file = project_root / "optimized_structure_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 详细结果已保存到: {output_file}")
        
        # 6. 测试结果评估
        print("\n📈 6. 测试结果评估")
        print("-" * 30)
        
        success_criteria = [
            (cover_info.get('has_cover', False), "检测到封面页"),
            (len(outline) > 0, "识别到标题"),
            (centered_count > 0, "识别到居中标题"),
            (structure_elements['声明'], "找到声明"),
            (structure_elements['授权书'], "找到授权书"),
            (structure_elements['摘要'], "找到摘要"),
            (structure_elements['参考文献'], "找到参考文献")
        ]
        
        passed = sum(1 for criteria, _ in success_criteria if criteria)
        total = len(success_criteria)
        
        print(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        print()
        
        for criteria, description in success_criteria:
            status = "✅" if criteria else "❌"
            print(f"   {status} {description}")
        
        return passed >= total * 0.8  # 80%通过率
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 优化后的文档结构分析测试")
    print("=" * 60)
    
    success = test_optimized_structure()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功 - 优化后的结构分析工作正常")
        sys.exit(0)
    else:
        print("❌ 测试失败 - 需要进一步优化")
        sys.exit(1)

if __name__ == "__main__":
    main()
