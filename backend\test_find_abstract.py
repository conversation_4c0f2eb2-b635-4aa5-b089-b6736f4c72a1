#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找中文摘要
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import WordInstancePool

def test_find_abstract():
    """查找中文摘要"""
    print("🧪 查找中文摘要")
    print("=" * 60)
    
    doc_path = r"D:\Works\paper-check-win\docs\test.docx"
    print(f"📄 测试文档: {doc_path}")
    print("=" * 60)
    
    # 初始化资源管理器
    resource_manager = WordInstancePool()
    
    try:
        # 获取Word实例
        with resource_manager.get_instance() as word_instance:
            doc = word_instance.open_document(doc_path)
            
            print("🔍 逐页检查摘要相关内容:")
            print("-" * 50)
            
            # 检查第9-12页
            for page_num in range(9, 13):
                try:
                    print(f"\n📄 第{page_num}页:")
                    
                    # 获取该页的所有段落
                    paragraphs = doc.Paragraphs
                    page_paragraphs = []
                    
                    for i, para in enumerate(paragraphs):
                        try:
                            para_page = para.Range.Information(3)  # wdActiveEndPageNumber
                            if para_page == page_num:
                                para_text = para.Range.Text.strip()
                                if para_text:  # 只显示非空段落
                                    page_paragraphs.append((i, para_text, para.Style.NameLocal))
                        except:
                            continue
                    
                    # 显示该页的段落
                    for i, (para_idx, para_text, style) in enumerate(page_paragraphs[:10]):  # 只显示前10个段落
                        preview = para_text.replace('\r', ' ').replace('\n', ' ')[:100]
                        print(f"   段落{para_idx}: '{preview}' (样式: {style})")
                        
                        # 检查是否包含"摘要"
                        if "摘要" in para_text:
                            print(f"   ⭐ 发现'摘要': '{para_text}' (样式: {style})")
                    
                    if len(page_paragraphs) > 10:
                        print(f"   ... 还有 {len(page_paragraphs) - 10} 个段落")
                        
                except Exception as e:
                    print(f"   第{page_num}页检查失败: {e}")
            
            word_instance.close_document()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        resource_manager.shutdown()

if __name__ == "__main__":
    test_find_abstract()
