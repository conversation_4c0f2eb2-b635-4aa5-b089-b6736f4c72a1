#!/usr/bin/env python3
"""
上传测试文档并创建检测任务
"""

import requests
import json
import os

def login_and_get_token():
    """登录并获取token"""
    
    base_url = "http://localhost:8000/api/v1"
    
    # 登录数据
    login_data = "username=8966097&password=heibailan5112"
    
    try:
        print("🚀 尝试登录...")
        response = requests.post(
            f"{base_url}/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            data = response.json()
            # 检查响应格式
            if data.get('success') and data.get('data'):
                token = data['data'].get('access_token')
            else:
                token = data.get('access_token')

            if token:
                print(f"✅ 登录成功，获取到token: {token[:20]}...")
                return token
            else:
                print("❌ 登录响应中没有token")
                print(f"响应: {data}")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"响应: {response.text}")
            
        return None
        
    except Exception as e:
        print(f"❌ 登录失败: {str(e)}")
        return None

def upload_document(token):
    """上传文档并创建任务"""
    
    base_url = "http://localhost:8000/api/v1"
    doc_path = "D:/Works/paper-check-win/docs/test.docx"
    
    if not os.path.exists(doc_path):
        print(f"❌ 测试文档不存在: {doc_path}")
        return None
    
    try:
        print(f"🚀 上传文档: {doc_path}")
        
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # 准备文件和数据
        with open(doc_path, 'rb') as f:
            files = {
                'file': ('test.docx', f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            }
            
            data = {
                'task_type': 'paper_check',
                'options': json.dumps({
                    'detection_standard': 'hbkj_bachelor_2024',
                    'check_structure': True
                }),
                'description': '测试文档结构检测'
            }
            
            response = requests.post(
                f"{base_url}/tasks/upload",
                files=files,
                data=data,
                headers=headers
            )
        
        if response.status_code in [200, 201]:
            data = response.json()
            task_id = data.get('task_id')
            if task_id:
                print(f"✅ 文档上传成功，任务ID: {task_id}")
                return task_id
            else:
                print("❌ 上传响应中没有任务ID")
                print(f"响应: {data}")
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            print(f"响应: {response.text}")
            
        return None
        
    except Exception as e:
        print(f"❌ 上传文档失败: {str(e)}")
        return None

def wait_for_task_completion(token, task_id, max_wait=300):
    """等待任务完成"""
    
    base_url = "http://localhost:8000/api/v1"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    import time
    
    print(f"🚀 等待任务完成: {task_id}")
    
    for i in range(max_wait):
        try:
            response = requests.get(
                f"{base_url}/tasks/{task_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    task_data = data['data']
                    status = task_data.get('status')
                    progress = task_data.get('progress', 0)
                    
                    print(f"📊 任务状态: {status}, 进度: {progress}%")
                    
                    if status == 'completed':
                        print(f"✅ 任务完成！")
                        
                        # 检查结果
                        result = task_data.get('result', {})
                        if result:
                            structures = result.get('document_structures', [])
                            print(f"📊 检测到 {len(structures)} 个文档结构")
                            
                            if structures:
                                print(f"📝 前3个结构:")
                                for j, structure in enumerate(structures[:3], 1):
                                    name = structure.get('structure_name') or structure.get('name', 'Unknown')
                                    type_info = structure.get('type', 'unknown')
                                    page = structure.get('page', 'unknown')
                                    print(f"  {j}. {name} (类型: {type_info}, 页面: {page})")
                        
                        return True
                    elif status == 'failed':
                        print(f"❌ 任务失败")
                        error_msg = task_data.get('error_message', '未知错误')
                        print(f"错误信息: {error_msg}")
                        return False
                    
            time.sleep(2)  # 等待2秒后重试
            
        except Exception as e:
            print(f"❌ 检查任务状态失败: {str(e)}")
            time.sleep(2)
    
    print(f"❌ 任务等待超时")
    return False

if __name__ == "__main__":
    print("🚀 开始上传测试文档...")
    
    # 1. 登录获取token
    token = login_and_get_token()
    if not token:
        print("💥 无法获取认证token！")
        exit(1)
    
    # 2. 上传文档
    task_id = upload_document(token)
    if not task_id:
        print("💥 文档上传失败！")
        exit(1)
    
    # 3. 等待任务完成
    success = wait_for_task_completion(token, task_id)
    
    if success:
        print(f"\n🎉 测试完成！")
        print(f"📊 统计报告链接: http://localhost:3000/statistics-report/{task_id}")
    else:
        print(f"\n💥 任务处理失败！")
