import apiService from './api'
import type { ApiResponse } from '@/types'

export interface SystemStats {
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
  pending_tasks: number
  processing_tasks: number
  total_documents: number
  total_problems: number
  avg_compliance_score?: number | null
}

export interface ProblemStats {
  severity: string
  count: number
  percentage: number
}

export const systemApi = {
  getStats: async (): Promise<SystemStats> => {
    return apiService.get('/v1/system/stats')
  },
  
  getProblemStats: async (): Promise<ProblemStats[]> => {
    return apiService.get('/v1/system/stats/problems')
  }
} 