#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文摘要检测
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import ResourceManager

def test_abstract_detection():
    """测试中文摘要检测"""
    print("🧪 测试中文摘要检测")
    print("=" * 60)
    
    doc_path = r"D:\Works\paper-check-win\docs\test.docx"
    print(f"📄 测试文档: {doc_path}")
    print("=" * 60)
    
    # 初始化资源管理器
    resource_manager = ResourceManager()
    
    try:
        # 初始化文档处理器
        processor = DocumentProcessor(resource_manager)
        
        # 分析文档结构
        print("📊 开始分析文档结构...")
        result = processor.analyze_document_structure(doc_path, method="rule_based")
        
        # 检查每一页的内容，寻找"摘要"
        print("\n🔍 逐页检查摘要相关内容:")
        print("-" * 50)
        
        # 获取页面内容
        with resource_manager.get_word_instance() as word_instance:
            doc = word_instance.open_document(doc_path)
            
            # 检查每一页
            for page_num in range(1, min(15, doc.ComputeStatistics(2) + 1)):  # 只检查前15页
                try:
                    # 获取该页的所有段落
                    page_range = doc.GoTo(1, 1, page_num)  # wdGoToPage
                    page_range.Expand(6)  # wdPage
                    page_text = page_range.Text.strip()
                    
                    # 检查是否包含"摘要"
                    if "摘要" in page_text:
                        print(f"   第{page_num}页: 发现'摘要'")
                        # 显示前200个字符
                        preview = page_text.replace('\r', ' ').replace('\n', ' ')[:200]
                        print(f"   内容预览: {preview}...")
                        
                        # 检查段落级别的内容
                        paragraphs = doc.Paragraphs
                        for i, para in enumerate(paragraphs):
                            if para.Range.Information(3) == page_num:  # wdActiveEndPageNumber
                                para_text = para.Range.Text.strip()
                                if "摘要" in para_text and len(para_text) < 50:  # 可能是标题
                                    print(f"   段落{i}: '{para_text}' (样式: {para.Style.NameLocal})")
                        print()
                        
                except Exception as e:
                    print(f"   第{page_num}页检查失败: {e}")
            
            word_instance.close_document()
        
        # 检查检测结果
        print("\n📋 检测结果:")
        print("-" * 30)
        structures = result.get('document_structures', [])
        for struct in structures:
            if struct['name'] == '中文摘要':
                print(f"   中文摘要状态: {struct['status']}")
                if struct['status'] == 'present':
                    print(f"   页码: {struct.get('page', 'N/A')}")
                    print(f"   内容: {struct.get('content', {}).get('text', 'N/A')}")
                break
        else:
            print("   中文摘要: 未在结果中找到")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        resource_manager.cleanup()

if __name__ == "__main__":
    test_abstract_detection()
