#!/usr/bin/env python3
"""
测试任务API返回的数据结构
"""

import json
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import get_db_session
from app.database import crud

async def test_task_data():
    """测试任务数据结构"""
    
    task_id = "test_structure_display_001"
    
    async for session in get_db_session():
        try:
            # 获取任务数据
            task = await crud.get_task(session, task_id)
            if not task:
                print(f"❌ 任务未找到: {task_id}")
                return False
            
            print(f"✅ 找到任务: {task.task_id}")
            print(f"📊 任务状态: {task.status}")
            print(f"📁 文件名: {task.filename}")
            print(f"🔍 任务对象类型: {type(task)}")
            print(f"🔍 任务属性: {dir(task)}")

            # 检查结果数据
            print(f"🔍 result属性: {hasattr(task, 'result')}")
            print(f"🔍 result值: {task.result}")
            print(f"🔍 result类型: {type(task.result)}")
            if hasattr(task, 'result') and task.result:
                result = task.result
                print(f"\n📋 结果数据结构:")
                print(f"  - document_structures: {len(result.get('document_structures', []))} 个")
                print(f"  - outline: {len(result.get('outline', []))} 个")
                
                # 显示前5个结构
                structures = result.get('document_structures', [])
                if structures:
                    print(f"\n📝 前5个文档结构:")
                    for i, structure in enumerate(structures[:5], 1):
                        name = structure.get('structure_name') or structure.get('name', 'Unknown')
                        type_info = structure.get('type', 'unknown')
                        page = structure.get('page', 'unknown')
                        print(f"  {i}. {name} (类型: {type_info}, 页面: {page})")
                
                # 输出完整数据到文件
                with open('task_result_debug.json', 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"\n💾 完整结果数据已保存到: task_result_debug.json")
                
                return True
            else:
                print("❌ 任务没有结果数据")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🚀 开始测试任务API数据...")
    success = asyncio.run(test_task_data())
    
    if success:
        print("\n🎉 测试完成！")
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
