#!/usr/bin/env python3
"""
基于规则的文档结构检测测试

测试目标：
1. 验证基于规则文件的结构检测功能
2. 按页面顺序检测文档结构
3. 使用规则中的标识符进行匹配
4. 排除表格内容
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_rule_based_structure():
    """测试基于规则的文档结构检测"""
    
    try:
        from app.services.document_processor import DocumentProcessor
        
        # 测试文档路径
        test_doc_path = project_root.parent / "docs" / "test.docx"
        
        if not test_doc_path.exists():
            print(f"❌ 测试文档不存在: {test_doc_path}")
            return False
        
        print(f"🔍 测试基于规则的文档结构检测")
        print(f"📄 测试文档: {test_doc_path}")
        print("=" * 60)
        
        # 初始化处理器
        processor = DocumentProcessor()
        
        # 分析文档结构
        print("\n📊 开始基于规则的结构分析...")
        structure = processor.analyze_document_structure(str(test_doc_path))
        
        # 1. 检查分析方法
        print("\n🔧 1. 分析方法检查")
        print("-" * 30)
        
        analysis_method = structure.get('structure_analysis_method', 'unknown')
        print(f"   分析方法: {analysis_method}")
        
        if analysis_method == 'rule_based':
            print("   ✅ 成功使用基于规则的分析方法")
        elif analysis_method == 'fallback':
            print("   ⚠️ 使用了备用分析方法")
        else:
            print("   ❌ 分析方法未知")
        
        # 2. 检查检测到的文档结构
        print("\n📋 2. 检测到的文档结构")
        print("-" * 30)
        
        document_structures = structure.get('document_structures', [])
        if document_structures:
            print(f"   检测到 {len(document_structures)} 个结构:")
            
            for i, struct in enumerate(document_structures, 1):
                name = struct.get('name', '未知')
                status = struct.get('status', 'unknown')
                page = struct.get('page', 'N/A')
                required = struct.get('required', False)
                
                status_icon = {
                    'present': '✅',
                    'missing': '❌'
                }.get(status, '❓')
                
                required_text = " (必需)" if required else " (可选)"
                page_text = f" [第{page}页]" if page != 'N/A' else ""
                
                print(f"     {i:2d}. {status_icon} {name}{required_text}{page_text}")
                
                # 显示匹配的内容
                if status == 'present':
                    content = struct.get('content', {})
                    text = content.get('text', '')
                    if text:
                        text_display = text[:40] + '...' if len(text) > 40 else text
                        print(f"         内容: {text_display}")
        else:
            print("   ❌ 未检测到任何文档结构")
        
        # 3. 检查outline兼容性
        print("\n📝 3. Outline兼容性检查")
        print("-" * 30)
        
        outline = structure.get('outline', [])
        print(f"   Outline条目数: {len(outline)}")
        
        if outline:
            print("   前5个outline条目:")
            for i, item in enumerate(outline[:5], 1):
                text = item.get('text', '')
                text_display = text[:30] + '...' if len(text) > 30 else text
                item_type = item.get('type', 'unknown')
                level = item.get('level', 0)
                page = item.get('page', 'N/A')
                
                type_icon = {
                    'rule_based': '📋',
                    'style': '🎨',
                    'centered': '📍',
                    'unknown': '❓'
                }.get(item_type, '❓')
                
                print(f"     {i}. {type_icon} H{level} [页{page}] | {text_display}")
        
        # 4. 规则匹配统计
        print("\n📊 4. 规则匹配统计")
        print("-" * 30)
        
        if document_structures:
            present_count = sum(1 for s in document_structures if s['status'] == 'present')
            missing_count = sum(1 for s in document_structures if s['status'] == 'missing')
            required_missing = sum(1 for s in document_structures if s['status'] == 'missing' and s.get('required'))
            
            print(f"   - 检测到的结构: {present_count}")
            print(f"   - 缺失的结构: {missing_count}")
            print(f"   - 缺失的必需结构: {required_missing}")
            
            # 检查关键结构
            key_structures = ['封面', '任务书', '开题报告', '诚信声明', '版权声明', '中文摘要', '参考文献']
            found_key_structures = []
            
            for struct in document_structures:
                if struct['name'] in key_structures and struct['status'] == 'present':
                    found_key_structures.append(struct['name'])
            
            print(f"   - 找到的关键结构: {len(found_key_structures)}/{len(key_structures)}")
            print(f"     {', '.join(found_key_structures)}")
        
        # 5. 保存测试结果
        print("\n💾 5. 保存测试结果")
        print("-" * 30)
        
        output_file = project_root / "rule_based_structure_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试结果已保存到: {output_file}")
        
        # 6. 测试结果评估
        print("\n📈 6. 测试结果评估")
        print("-" * 30)
        
        success_criteria = [
            (analysis_method in ['rule_based', 'fallback'], "使用了有效的分析方法"),
            (len(document_structures) > 0, "检测到文档结构"),
            (len(outline) > 0, "生成了outline"),
            (any(s['name'] == '封面' and s['status'] == 'present' for s in document_structures), "检测到封面"),
            (any(s['name'] == '任务书' and s['status'] == 'present' for s in document_structures), "检测到任务书"),
            (any(s['name'] == '开题报告' and s['status'] == 'present' for s in document_structures), "检测到开题报告")
        ]
        
        passed = sum(1 for criteria, _ in success_criteria if criteria)
        total = len(success_criteria)
        
        print(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        print()
        
        for criteria, description in success_criteria:
            status = "✅" if criteria else "❌"
            print(f"   {status} {description}")
        
        return passed >= total * 0.8  # 80%通过率
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 基于规则的文档结构检测测试")
    print("=" * 60)
    
    success = test_rule_based_structure()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功 - 基于规则的结构检测工作正常")
        sys.exit(0)
    else:
        print("❌ 测试失败 - 需要进一步优化")
        sys.exit(1)

if __name__ == "__main__":
    main()
