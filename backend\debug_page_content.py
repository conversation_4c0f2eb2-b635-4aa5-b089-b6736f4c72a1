#!/usr/bin/env python3
"""
调试页面内容分配
"""

import sys
import os
sys.path.append('.')

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import WordInstancePool

def debug_page_content():
    """调试页面内容分配"""

    # 初始化资源管理器
    resource_manager = WordInstancePool()
    processor = DocumentProcessor(resource_manager)
    
    doc_path = r'D:\Works\paper-check-win\docs\test.docx'
    
    try:
        with resource_manager.get_instance() as word_instance:
            word_instance.open_document(doc_path)
            
            # 提取页面内容
            pages_content = processor._extract_pages_content(word_instance)
            
            word_instance.close_document()
        
        print("🔍 查找包含关键词的页面内容")
        print("=" * 80)
        
        # 查找包含关键词的页面
        for page_num, page_data in pages_content.items():
            page_paragraphs = page_data.get('paragraphs', [])
            
            for i, paragraph in enumerate(page_paragraphs):
                text = paragraph.get('text', '')
                
                # 检查是否包含关键词
                if '关键词：' in text or 'Key words:' in text:
                    print(f"\n📍 第{page_num}页，段落{i+1}:")
                    print(f"   完整文本: '{text}'")
                    print(f"   文本长度: {len(text)}")
                    
                    # 测试关键词检测
                    import re
                    
                    # 中文关键词检测
                    pattern_cn = r'^(关键词|关键字)\s*[：:]\s*(.+)'
                    match_cn = re.match(pattern_cn, text.strip())
                    if match_cn:
                        print(f"   ✅ 匹配中文关键词模式")
                        print(f"   关键词内容: '{match_cn.group(2)}'")
                    else:
                        print(f"   ❌ 不匹配中文关键词模式")
                    
                    # 英文关键词检测
                    pattern_en = r'^(keywords?|key\s+words?)\s*[：:]\s*(.+)'
                    match_en = re.match(pattern_en, text.strip(), re.IGNORECASE)
                    if match_en:
                        print(f"   ✅ 匹配英文关键词模式")
                        print(f"   关键词内容: '{match_en.group(2)}'")
                    else:
                        print(f"   ❌ 不匹配英文关键词模式")
        
        print("\n" + "=" * 80)
        print("🔍 检查第11页和第12页的所有段落")
        
        # 检查特定页面的所有段落
        for page_num in [11, 12]:
            if page_num in pages_content:
                page_data = pages_content[page_num]
                page_paragraphs = page_data.get('paragraphs', [])
                
                print(f"\n📄 第{page_num}页内容 (共{len(page_paragraphs)}个段落):")
                
                for i, paragraph in enumerate(page_paragraphs):
                    text = paragraph.get('text', '')
                    if text.strip():  # 只显示非空段落
                        print(f"   段落{i+1}: {text[:100]}...")
                        
                        # 如果包含关键词相关内容，详细分析
                        if any(keyword in text.lower() for keyword in ['关键词', '关键字', 'keywords', 'key words']):
                            print(f"      🎯 可能的关键词段落!")
                            print(f"      完整文本: '{text}'")
            else:
                print(f"\n📄 第{page_num}页: 未找到页面数据")
        
        print("\n" + "=" * 80)
        print(f"📊 总页数: {len(pages_content)}")
        
    except Exception as e:
        print(f"❌ 处理文档时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_page_content()
