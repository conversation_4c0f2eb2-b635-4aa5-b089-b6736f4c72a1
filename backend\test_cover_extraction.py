#!/usr/bin/env python3
"""
测试封面信息提取功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from app.services.document_processor import DocumentProcessor
from app.services.document_analyzer import DocumentAnalyzer
from app.core.logging import logger

async def test_cover_extraction():
    """测试封面信息提取"""
    
    test_file = "data/uploads/user_2450b8b44a9c4db0842e36cd9e99ed65/task_4b57c320031744928868f02913129108_test.docx"
    
    if not Path(test_file).exists():
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print("🔍 测试封面信息提取功能")
    print("="*60)
    
    try:
        # 1. 测试DocumentProcessor的封面信息提取
        print("📄 1. 测试DocumentProcessor封面信息提取...")
        processor = DocumentProcessor()
        
        # 获取文档结构信息（包含封面页信息）
        structure_info = processor.analyze_document_structure(test_file)
        cover_info = structure_info.get('cover_page_info', {})
        
        print(f"📋 封面信息提取结果:")
        for key, value in cover_info.items():
            if key != 'raw_text':  # 跳过原始文本，太长了
                print(f"   {key}: {value}")
        
        print(f"📝 原始文本长度: {len(cover_info.get('raw_text', ''))}")
        
        # 2. 测试DocumentAnalyzer的完整分析
        print("\n🔬 2. 测试DocumentAnalyzer完整分析...")
        analyzer = DocumentAnalyzer()
        
        analysis_result = await analyzer.analyze_document(test_file)
        
        print(f"✅ 分析成功: {analysis_result.success}")
        if hasattr(analysis_result, 'document_info'):
            doc_info = analysis_result.document_info
            print(f"📄 document_info类型: {type(doc_info)}")
            
            if isinstance(doc_info, dict):
                cover_page_info = doc_info.get('cover_page_info', {})
                print(f"📋 document_info中的封面信息:")
                for key, value in cover_page_info.items():
                    if key != 'raw_text':
                        print(f"   {key}: {value}")
            else:
                print(f"⚠️ document_info不是字典类型: {doc_info}")
        else:
            print("❌ analysis_result没有document_info属性")
        
        # 3. 测试任务管理器的转换逻辑
        print("\n🔄 3. 测试任务管理器转换逻辑...")
        from app.tasks.manager import TaskManager
        
        task_manager = TaskManager()
        converted_result = task_manager._convert_analysis_result_to_dict(analysis_result)
        
        print(f"📊 转换结果类型: {type(converted_result)}")
        document_info = converted_result.get('document_info', {})
        print(f"📄 转换后的document_info:")
        
        if isinstance(document_info, dict):
            cover_page_info = document_info.get('cover_page_info', {})
            print(f"📋 转换后的封面信息:")
            for key, value in cover_page_info.items():
                if key != 'raw_text':
                    print(f"   {key}: {value}")
        else:
            print(f"⚠️ 转换后的document_info不是字典: {document_info}")
        
        print("\n" + "="*60)
        print("🎯 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_cover_extraction())
