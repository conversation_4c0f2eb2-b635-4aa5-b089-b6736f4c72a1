#!/usr/bin/env python3
"""
测试文档结构检测并输出JSON格式数据
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.document_processor import DocumentProcessor

def test_document_structures():
    """测试文档结构检测并输出JSON数据"""
    
    # 初始化文档处理器
    processor = DocumentProcessor()
    
    # 测试文档路径
    doc_path = "D:/Works/paper-check-win/docs/test.docx"
    
    if not os.path.exists(doc_path):
        print(f"❌ 测试文档不存在: {doc_path}")
        return False
    
    try:
        # 分析文档结构
        print("📊 开始结构分析...")
        result = processor.analyze_document_structure(doc_path)
        
        # 提取document_structures数据
        document_structures = result.get('document_structures', [])
        
        print(f"\n📋 检测到的文档结构 ({len(document_structures)}个)")
        print("-" * 50)
        
        for i, structure in enumerate(document_structures, 1):
            print(f"{i:2d}. {structure.get('structure_name', 'Unknown')}")
            print(f"    类型: {structure.get('type', 'unknown')}")
            print(f"    页面: {structure.get('page', 'unknown')}")
            if structure.get('content'):
                print(f"    内容: {structure['content'].get('text', '')[:50]}...")
            print()
        
        # 输出到JSON文件
        output_data = {
            "document_structures": document_structures,
            "total_count": len(document_structures),
            "analysis_method": result.get('structure_analysis_method', 'unknown'),
            "outline": result.get('outline', [])
        }
        
        output_file = "document_structures_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结构数据已输出到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试文档结构检测...")
    success = test_document_structures()
    
    if success:
        print("\n🎉 测试完成！")
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
