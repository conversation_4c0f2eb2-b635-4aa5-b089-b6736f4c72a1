#!/usr/bin/env python3
"""
调试文档中的关键词内容
"""

import sys
import os
sys.path.append('.')

from app.services.word_com import WordApplication

def debug_document_keywords():
    """调试文档中的关键词内容"""
    
    doc_path = r'D:\Works\paper-check-win\docs\test.docx'
    
    # 创建Word应用程序实例
    word_app = WordApplication()
    
    try:
        # 启动Word应用程序
        word_app.start()
        
        # 打开文档并获取文档对象
        doc = word_app.open_document(doc_path)
        
        print("🔍 查找文档中包含'关键词'或'keywords'的段落")
        print("=" * 80)
        
        # 遍历所有段落
        for i, paragraph in enumerate(doc.Paragraphs):
            text = paragraph.Range.Text.strip()
            
            # 检查是否包含关键词相关内容
            if any(keyword in text.lower() for keyword in ['关键词', '关键字', 'keywords', 'key words']):
                print(f"\n📍 段落 {i+1}:")
                print(f"   原始文本: '{text}'")
                print(f"   文本长度: {len(text)}")
                print(f"   文本编码: {repr(text)}")
                
                # 检查段落格式
                try:
                    alignment = paragraph.Range.ParagraphFormat.Alignment
                    font_size = paragraph.Range.Font.Size
                    style = paragraph.Style.NameLocal
                    print(f"   对齐方式: {alignment}")
                    print(f"   字体大小: {font_size}")
                    print(f"   样式: {style}")
                except Exception as e:
                    print(f"   格式信息获取失败: {e}")
                
                # 测试关键词检测
                import re
                
                # 中文关键词检测
                pattern_cn = r'^(关键词|关键字)\s*[：:]\s*(.+)'
                match_cn = re.match(pattern_cn, text)
                if match_cn:
                    print(f"   ✅ 匹配中文关键词模式")
                    print(f"   关键词内容: '{match_cn.group(2)}'")
                else:
                    print(f"   ❌ 不匹配中文关键词模式")
                
                # 英文关键词检测
                pattern_en = r'^(keywords?|key\s+words?)\s*[：:]\s*(.+)'
                match_en = re.match(pattern_en, text, re.IGNORECASE)
                if match_en:
                    print(f"   ✅ 匹配英文关键词模式")
                    print(f"   关键词内容: '{match_en.group(2)}'")
                else:
                    print(f"   ❌ 不匹配英文关键词模式")
        
        print("\n" + "=" * 80)
        print("🔍 查找第11页和第12页的所有段落")
        
        # 查找特定页面的内容
        for page_num in [11, 12]:
            print(f"\n📄 第{page_num}页内容:")
            
            # 获取页面范围
            try:
                # 这是一个简化的方法，实际可能需要更复杂的页面检测
                for i, paragraph in enumerate(doc.Paragraphs):
                    text = paragraph.Range.Text.strip()
                    if text and len(text) > 5:  # 过滤空段落
                        print(f"   段落 {i+1}: {text[:100]}...")
                        
                        # 如果包含关键词相关内容，详细分析
                        if any(keyword in text.lower() for keyword in ['关键词', '关键字', 'keywords', 'key words']):
                            print(f"      🎯 可能的关键词段落!")
                            print(f"      完整文本: '{text}'")
                            
            except Exception as e:
                print(f"   页面内容获取失败: {e}")
        
    except Exception as e:
        print(f"❌ 处理文档时出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭文档和应用程序
        try:
            word_app.close_document()
            word_app.quit()
        except:
            pass

if __name__ == "__main__":
    debug_document_keywords()
